"use client"

import Link from "next/link"
import { useSession } from 'next-auth/react'
import { User, RefreshCw } from "lucide-react"
import { useState, useEffect } from 'react'
import { UserDropdownMenu } from "@/components/user/menu"
import { Button } from "@/components/ui/button"

export function UserMenu() {
  const { data: session, status } = useSession()
  const [loadingTimeout, setLoadingTimeout] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  // 设置加载超时检测
  useEffect(() => {
    if (status === 'loading') {
      const timer = setTimeout(() => {
        setLoadingTimeout(true)
        console.warn('用户认证加载超时，可能存在网络问题')
      }, 10000) // 10秒超时

      return () => clearTimeout(timer)
    } else {
      setLoadingTimeout(false)
    }
  }, [status])

  // 重试认证
  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    setLoadingTimeout(false)
    
    // 刷新页面来重新初始化认证
    if (retryCount >= 2) {
      // 多次重试后，清除缓存并硬刷新
      localStorage.clear()
      sessionStorage.clear()
      window.location.reload()
    } else {
      // 简单刷新
      window.location.reload()
    }
  }

  // 如果加载超时，显示重试按钮
  if (loadingTimeout) {
    return (
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleRetry}
          className="flex items-center gap-2"
        >
          <RefreshCw size={16} />
          <span className="hidden sm:inline">重试</span>
        </Button>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2">
      {status === 'loading' ? (
        <div className="flex items-center space-x-2 px-3 py-2">
          <div className="w-5 h-5 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
          <span className="hidden sm:inline text-sm text-muted-foreground">加载中...</span>
        </div>
      ) : session?.user ? (
        <UserDropdownMenu image={session.user.image || null} />
      ) : (
        <Link
          href="/login"
          className="flex items-center space-x-2 hover:text-primary transition-colors px-3 py-2 rounded-lg hover:bg-muted/30"
        >
          <User size="20" className="inline-block" />
          <span className="hidden sm:inline">登录</span>
        </Link>
      )}
    </div>
  )
} 