/** @type {import('next').NextConfig} */
const nextConfig = {
  // 静态导出配置，用于Capacitor打包
  output: 'export',
  swcMinify: true,
  // 禁用图片优化，因为静态导出不支持
  images: {
    unoptimized: true,
  },

   // 👇 添加 compiler 配置，让 SWC 支持旧浏览器
   compiler: {
    // 开启 styled-jsx 支持（如果用了）
    styledComponents: true,
  },

  // webpack: (config) => {
  //   // if (!isServer) {
  //   //   // 只在客户端构建时设置target
  //   //   config.target = ['web', 'es5']
  //   // }
  //   config.cache = true;
  //   return config 
  // },

  // 关闭严格模式以避免某些兼容性问题
  reactStrictMode: false,
  
  // 禁用 trailing slash，保持URL简洁
  trailingSlash: true,
}

module.exports = nextConfig 