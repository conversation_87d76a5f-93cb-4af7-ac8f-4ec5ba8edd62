"use client"

import { Suspense, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { LoginForm } from "@/components/auth/login-form"
import { useAuth } from "@/hooks/use-auth"
import MobileNav from "@/components/layout/mobile/head-nav"

// 将metadata移到其他地方或者移除，因为客户端组件不支持metadata
// export const metadata: Metadata = {
//   title: "登录 - 命理学研习社",
//   description: "登录您的命理学研习社账号",
// }

function LoginPageContent() {
  const { isAuthenticated, isLoading } = useAuth()
  //const router = useRouter()
  const searchParams = useSearchParams()
  const callbackUrl = searchParams.get('callbackUrl') || '/'

  // useEffect(() => {
  //   // 如果用户已登录，重定向到回调URL
  //   if (!isLoading && isAuthenticated) {
  //     console.log("User already logged in, redirecting to:", callbackUrl)
  //     router.push(decodeURIComponent(callbackUrl))
  //   }
  // }, [isAuthenticated, isLoading, callbackUrl, router])

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-full flex items-center justify-center p-4 bg-paper-secondary w-full">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="mt-2 text-sm text-muted-foreground">正在加载...</p>
        </div>
      </div>
    )
  }

  return (
      <div className="h-full flex items-center justify-center bg-background w-full">
        <div className="h-full w-full overflow-hidden">
          <div className="py-8 px-12">
            <h2 className="text-2xl font-serif text-primary text-center mb-6"> 用户登录</h2>
            <LoginForm callbackUrl={callbackUrl} />
          </div>
        </div>
      </div>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <div className="flex flex-1 flex-col h-full">
        <MobileNav title="" showDropdownMenu={false}/>
        <LoginPageContent />
      </div>
    </Suspense>
  )
} 