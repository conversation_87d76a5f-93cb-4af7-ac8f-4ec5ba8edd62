'use client';

export default function GlobalError({
    error,
    reset,
}: {
    error: Error & { digest?: string }
    reset: () => void
}) {
    console.error('[GlobalError]', error);
    return (
        <html>
            <body>
                <div className="min-h-dvh flex flex-col items-center justify-center gap-4 p-6">
                    <h1 className="text-xl font-semibold">应用发生异常</h1>
                    <p className="text-muted-foreground">请刷新或稍后再试</p>
                    <div className="flex gap-3">
                        <button
                            onClick={() => reset()}
                            className="rounded-xl border px-4 py-2"
                        >
                            重试
                        </button>
                    </div>
                </div>
            </body>
        </html>
    );
}
