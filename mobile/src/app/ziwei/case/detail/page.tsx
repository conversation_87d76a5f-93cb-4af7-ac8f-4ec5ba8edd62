"use client"

import React from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useCaseDetail } from '@/hooks/use-api'
import { buildBirthDataFromCase, buildSubmitIDFromCase } from '@/lib/case-utils'
import CaseContent from '@/components/case/case-content'

export default function CaseDetailPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  
  // 获取URL查询参数
  const caseId = searchParams.get('caseId')
  const { case: caseDetail, isLoading: isLoadingCase, isError: isErrorCase, error: caseError } = useCaseDetail(caseId)

  const handleBack = () => {
    router.back()
  }

  if (!caseId) {
    // 如果没有caseId参数，重定向到案例列表页
    router.push('/ziwei/case')
    return null
  }

  
  if (isLoadingCase) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-stone-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-primary rounded-full animate-spin mx-auto"></div>
          <p className="text-gray-600">加载案例数据中...</p>
        </div>
      </div>
    )
  }
  
  if (!caseDetail || isErrorCase || caseError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-stone-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-red-500 text-5xl">⚠️</div>
          <p className="text-gray-600">案例数据加载失败</p>
          <Button onClick={handleBack} variant="outline">
            返回
          </Button>
        </div>
      </div>
    )
  }

  const birthData = buildBirthDataFromCase(caseDetail)
  const submitID = buildSubmitIDFromCase(caseDetail)
  
  return (
    <CaseContent
      birthData={birthData}
      submitID={submitID}
      isSaveCaseDocument={false} // 这里必须是false，因为是沿用已有的数据，所以不能保存生成新case
      currentCaseId={caseDetail.case_id}
    />
  )
}