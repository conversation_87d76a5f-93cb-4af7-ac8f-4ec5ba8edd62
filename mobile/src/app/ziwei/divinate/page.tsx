"use client"

import { AuthGuard } from "@/components/auth/AuthGuard"
import DivinateForm from "@/components/divinate-form"
import { Suspense } from "react"

export default function ZiweiDivinatePage() {
  return (
    <div className="flex flex-1 w-full bg-background pb-safe-top justify-center items-center ">
      <div className="flex px-4 py-4 md:py-8 items-center justify-center w-full">
        <Suspense>
          <DivinateForm
            title="紫微斗数星盘"
            targetUrl="/ziwei/divinate/result"
            openInNewTab={false}
          />
        </Suspense>
      </div>
    </div>
  )
}