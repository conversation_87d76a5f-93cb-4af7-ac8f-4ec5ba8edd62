"use client"
import { Card, CardContent } from "@/components/ui/card"
import { useState, useEffect, useMemo, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { BirthData } from "@/types/user"
import Loading from "./loading"
import CaseContent from "@/components/case/case-content"

export default function ZiweiResultPage() {
  const searchParams = useSearchParams()
  const [birthData, setBirthData] = useState<BirthData | null>(null)
  const [submitID, setSubmitID] = useState<string>('')
  const [isSaveCaseDocument, setIsSaveCaseDocument] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 使用 useMemo 缓存解析后的URL参数，避免重复解析
  const parsedParams = useMemo(() => {
    const name = searchParams.get('name')
    const gender = searchParams.get('gender')
    const birthTime = searchParams.get('birthTime')
    const birthplace = searchParams.get('birthplace')
    const longitude = searchParams.get('longitude')
    const isLunar = searchParams.get('isLunar') === 'true'
    const useTrueSolarTime = searchParams.get('useTrueSolarTime') === 'true'
    const isDST = searchParams.get('isDST') === 'true'
    const useEarlyOrLateNight = searchParams.get('useEarlyOrLateNight') === 'true'
    const relationship = searchParams.get('relationship')
    const isSaveCaseDocument = searchParams.get('isSaveCaseDocument') === 'true'
    const submitID = searchParams.get('submitID')

    return {
      name, gender, birthTime, birthplace, longitude,
      isLunar, useTrueSolarTime, isDST, useEarlyOrLateNight,
      relationship, isSaveCaseDocument, submitID
    }
  }, [searchParams])

  // 解析URL参数并构建BirthData对象
  useEffect(() => {
    try {
      const {
        name, gender, birthTime, birthplace, longitude,
        isLunar, useTrueSolarTime, isDST, useEarlyOrLateNight,
        relationship, isSaveCaseDocument, submitID
      } = parsedParams

      // 验证必要参数
      if (!name || !gender || !birthTime || !birthplace || !relationship) {
        setError('缺少必要的出生信息参数')
        setIsLoading(false)
        return
      }

      // 构建 BirthData 对象
      const parsedBirthData: BirthData = {
        name,
        gender: gender as 'male' | 'female',
        birthTime,
        birthplace,
        longitude: parseFloat(longitude || '0'),
        isLunar,
        useTrueSolarTime,
        isDST,
        useEarlyOrLateNight,
        relationship: relationship as any,
      }

      setBirthData(parsedBirthData)
      setSubmitID(submitID || '')
      setIsSaveCaseDocument(isSaveCaseDocument)
      setIsLoading(false)
    } catch (err) {
      console.error('参数解析失败:', err)
      setError('参数解析失败，请检查URL参数')
      setIsLoading(false)
    }
  }, [parsedParams])

  // 如果正在加载，显示loading组件
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Loading />
      </div>
    )
  }

  // 如果解析出错，显示错误信息
  if (error || !birthData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="shadow-lg mb-8">
          <CardContent>
            <div className="flex flex-col items-center justify-center h-[400px] text-center">
              <p className="text-red-500 mb-4">{error || '数据加载失败'}</p>
              <button
                onClick={() => window.history.back()}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              >
                返回上一页
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex flex-col flex-1 h-full w-full bg-white md:px-4 md:py-8 pb-safe-top overflow-y-auto ">
      <Suspense fallback={
        <div className="container mx-auto px-4 py-8">
          <Loading />
        </div>
      }>
        <CaseContent
          birthData={birthData}
          submitID={submitID}
          isSaveCaseDocument={isSaveCaseDocument}
          currentCaseId={null}
        />
      </Suspense>
    </div>
  )
}