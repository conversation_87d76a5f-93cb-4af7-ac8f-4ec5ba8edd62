"use client"

import Link from 'next/link'
import { LogIn, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface LoginPromptProps {
  title?: string
  description?: string
  returnUrl?: string
}

/**
 * 登录提示组件
 * 用于引导未登录用户跳转到登录页面
 */
export function LoginPrompt({ 
  title = "需要登录", 
  description = "登录后即可查看您的个人案例和学习记录",
  returnUrl 
}: LoginPromptProps) {
  // 构建登录URL，包含回调参数
  const loginUrl = returnUrl ? `/login?callbackUrl=${encodeURIComponent(returnUrl)}` : '/login'

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-6">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
            <User className="h-8 w-8 text-primary" />
          </div>
          <CardTitle className="text-xl">{title}</CardTitle>
          <CardDescription className="text-base">
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild className="w-full" size="lg">
            <Link href={loginUrl} className="flex items-center justify-center gap-2 text-primary-foreground">
              <LogIn className="h-4 w-4 text-primary-foreground" />
              <p className="text-primary-foreground">立即登录</p>
            </Link>
          </Button>
          <div className="text-center">
            <Link 
              href="/register" 
              className="text-sm hover:text-primary transition-colors"
            >
              还没有账号？立即注册
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}