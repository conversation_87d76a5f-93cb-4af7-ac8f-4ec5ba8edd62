"use client"

import { useState, use<PERSON><PERSON>back, useEffect, useMemo } from "react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { DisplayMode } from "@/types/book"
import { Copy, Languages, BookOpen } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem, SelectGroup, SelectLabel } from "@/components/ui/select"

const readingModeOptions = [
  { value: "original", label: "原文", icon: BookOpen },
  { value: "translation", label: "译文", icon: Languages },
  { value: "both", label: "对照", icon: Copy },
]

interface BookDetailBottomProps {
  chapters: any[];
  main_chapter_level: number;
  selectedChapterValue: string;
  displayMode: string;
  setSelectedChapterId: (chapterId: string | null, chapterTitle: string) => void;
  handleDisplayMode: (mode: DisplayMode) => void;
}


export default function BookDetailBottom({ chapters, main_chapter_level, selectedChapterValue, displayMode, setSelectedChapterId, handleDisplayMode }:
  BookDetailBottomProps) {
  const [readingMode, setReadingMode] = useState(displayMode)
  const [progress, setProgress] = useState(1)
  const mainChapters = useMemo(() => chapters.filter((c) => c.level === main_chapter_level), [chapters, main_chapter_level])

  useEffect(() => {
    handleDisplayMode(readingMode as DisplayMode)
  }, [readingMode, handleDisplayMode])

  useEffect(() => {
    if (selectedChapterValue === 'cover') {
      setProgress(0)
      return
    }
    const chapterIndex = mainChapters.findIndex((c) => c.chapter_id === selectedChapterValue)
    const chapterNum = mainChapters.length
    if (chapterIndex !== -1) {
      setProgress((chapterIndex + 1) / chapterNum * 100)
    }
  }, [mainChapters, selectedChapterValue])

  const gotoNextChapter = useCallback(() => {
    const chapterIndex = mainChapters.findIndex((c) => c.chapter_id === selectedChapterValue)
    const chapterNum = mainChapters.length
    if (chapterIndex < chapterNum - 1) {
      setSelectedChapterId(mainChapters[chapterIndex + 1].chapter_id, mainChapters[chapterIndex + 1].title)
    }
  }, [selectedChapterValue, mainChapters, setSelectedChapterId])

  const gotoPrevChapter = useCallback(() => {
    const chapterIndex = mainChapters.findIndex((c) => c.chapter_id === selectedChapterValue)
    if (chapterIndex !== -1) {
      if (chapterIndex > 0) {
        setSelectedChapterId(mainChapters[chapterIndex - 1].chapter_id, mainChapters[chapterIndex - 1].title)
      }
      else {
        setSelectedChapterId(null, '')
      }
    }
  }, [selectedChapterValue, mainChapters, setSelectedChapterId])

  // 渲染章节菜单项的函数
  const renderChapterItems = () => {
    if (!chapters) return null;

    const items: JSX.Element[] = [];
    let currentGroup: JSX.Element[] = [];
    let currentGroupLabel = '';
    //添加封面页
    const coverPage = (
      <SelectItem key="cover" value="cover">
        封面
      </SelectItem>
    );
    items.push(coverPage)

    chapters.forEach((chapter) => {
      if (chapter.level < main_chapter_level) {
        // 如果有未完成的组，先添加到items中
        if (currentGroup.length > 0 && currentGroupLabel) {
          items.push(
            <SelectGroup key={currentGroupLabel}>
              <SelectLabel>{currentGroupLabel}</SelectLabel>
              {currentGroup}
            </SelectGroup>
          );
          currentGroup = [];
        }
        // 设置新的组标签
        currentGroupLabel = chapter.title;
      } else if (chapter.level === main_chapter_level) {
        // 添加到当前组或直接添加
        const item = (
          <SelectItem key={chapter.chapter_id} value={chapter.chapter_id}>
            {chapter.title}
          </SelectItem>
        );

        if (currentGroupLabel) {
          currentGroup.push(item);
        } else {
          console.error("章节错误", chapter.title);
        }
      }
      // level < main_chapter_level 的情况被忽略
    });

    // 处理最后一个组
    if (currentGroup.length > 0 && currentGroupLabel) {
      items.push(
        <SelectGroup key={currentGroupLabel}>
          <SelectLabel>{currentGroupLabel}</SelectLabel>
          {currentGroup}
        </SelectGroup>
      );
    }

    return items;
  };

  const handleChapterChange = useCallback((chapterValue: string) => {
    console.log("selectedChapterValue:", chapterValue)
    if (chapterValue === 'cover') {
      //cover
      setSelectedChapterId(null, '')
      return
    }
    const chapter = chapters.find((c) => c.chapter_id === chapterValue)
    if (chapter) {
      setSelectedChapterId(chapterValue, chapter.title)
    }
  }, [chapters, setSelectedChapterId])


  return (
    <>
      <Card className="flex pt-2 pb-4 w-full overflow-hidden border-0 bg-white/95 rounded-none backdrop-blur-xl shadow-2xl">
        <CardContent className="px-4 py-2 space-y-2">
          {/* 主要控制区域 - 一行布局 */}
          <div className="flex items-center gap-6 justify-between">
            <div onClick={gotoPrevChapter}>上一章</div>
            <Progress value={progress} className="flex-1" />
            <div onClick={gotoNextChapter}>下一章</div>
          </div>
          <div className="flex items-center gap-3 w-full">
            {/* 目录按钮 */}
            <Select onValueChange={handleChapterChange} value={selectedChapterValue}>
              <SelectTrigger className="flex-shrink-0 w-32 h-10 px-2 bg-gray-50/80 border-gray-200 hover:bg-gray-100/80 transition-all duration-200">
                {/* <Book className="h-4 w-4 mr-2" /> */}
                <SelectValue placeholder="目录" />
              </SelectTrigger>
              <SelectContent className='h-96 overflow-y-auto'>
                {renderChapterItems()}
              </SelectContent>
            </Select>

            {/* 阅读模式选择 */}
            <div className="flex-1">
              <RadioGroup value={readingMode} onValueChange={setReadingMode} className="flex gap-1">
                {readingModeOptions.map((option) => {
                  return (
                    <div key={option.value} className="flex-1">
                      <RadioGroupItem value={option.value} id={option.value} className="sr-only" />
                      <Label
                        htmlFor={option.value}
                        className={cn(
                          "flex flex-col items-center justify-center h-8 px-2 rounded-lg cursor-pointer transition-all duration-200 text-sm font-medium",
                          readingMode === option.value
                            ? "bg-primary text-white shadow-lg shadow-primary/25"
                            : "bg-paper-secondary text-gray-600 hover:bg-gray-100/80",
                        )}
                      >
                        {option.label}
                      </Label>
                    </div>
                  )
                })}
              </RadioGroup>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}