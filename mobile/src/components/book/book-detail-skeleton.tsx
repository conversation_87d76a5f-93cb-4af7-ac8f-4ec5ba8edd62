import { Skeleton } from "@/components/ui/skeleton"

/**
 * BookDetail页面的Skeleton加载状态组件
 * 模拟BookReader的封面页面布局
 */
export function BookDetailSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 头部导航栏Skeleton */}
      <div className="fixed top-0 left-0 right-0 z-10 bg-white/90 backdrop-blur-sm shadow-md">
        <div className="flex items-center justify-between px-4 py-3">
          <Skeleton className="h-6 w-6" />
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-6 w-6" />
        </div>
      </div>

      {/* 主要内容区域 - 封面页面Skeleton */}
      <div className="flex-1 pt-16 pb-20">
        <div className="h-full flex flex-col items-center justify-center px-8 space-y-6">
          {/* 书籍封面图片 */}
          <div className="flex flex-col items-center space-y-4">
            <Skeleton className="h-48 w-36 rounded-lg" />
            
            {/* 书籍标题 */}
            <Skeleton className="h-8 w-48" />
            
            {/* 作者信息 */}
            <Skeleton className="h-5 w-32" />
          </div>

          {/* 书籍描述 */}
          <div className="w-full max-w-md space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/4" />
          </div>

          {/* 开始阅读按钮区域 */}
          <div className="pt-4">
            <Skeleton className="h-12 w-32 rounded-full" />
          </div>
        </div>
      </div>

      {/* 底部功能栏Skeleton */}
      <div className="fixed bottom-0 left-0 right-0 z-10 bg-white/95 backdrop-blur-sm shadow-[0_-2px_5px_rgba(0,0,0,0.1)]">
        <div className="px-4 py-4 space-y-3">
          {/* 进度条区域 */}
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-2 flex-1 rounded-full" />
            <Skeleton className="h-8 w-16" />
          </div>
          
          {/* 功能按钮区域 */}
          <div className="flex items-center justify-between">
            {/* 目录选择器 */}
            <Skeleton className="h-10 w-32" />
            
            {/* 显示模式选择器 */}
            <div className="flex gap-2">
              <Skeleton className="h-8 w-12" />
              <Skeleton className="h-8 w-12" />
              <Skeleton className="h-8 w-12" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * 简化版的BookDetail Skeleton，用于快速加载场景
 */
export function BookDetailSkeletonSimple() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <Skeleton className="h-48 w-36 rounded-lg" />
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-4 w-32" />
        <div className="flex space-x-2 pt-4">
          <div className="w-2 h-2 bg-gray-300 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-gray-300 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-gray-300 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </div>
    </div>
  )
}