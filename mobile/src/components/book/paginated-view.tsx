"use client"
import { useRef, useState, useEffect, useMemo } from 'react';
import { ChapterContent } from '@/types/book';
import { DisplayMode } from '@/types/book';

interface PaginatedViewProps {
    chapterId: string;
    chapterTitle: string;
    content: ChapterContent;
    displayMode: DisplayMode;
    currentPage: number;
    setTotalPages: (count: number) => void;
    onGoToNext: () => void;
    onGoToPrev: () => void;
    onToggleBars: () => void;
}


export function PaginatedView({ chapterId, chapterTitle, content, displayMode, currentPage, setTotalPages, onGoToNext, onGoToPrev, onToggleBars }: PaginatedViewProps) {
    // 生成唯一实例ID用于调试

    const [paginatedContent, setPaginatedContent] = useState<string[]>([]);
    const [isPaginating, setIsPaginating] = useState(true); // 初始设为true，避免hydration mismatch
    const [previousContent, setPreviousContent] = useState<string>(''); // 保存上一次的内容，避免闪现

    const containerRef = useRef<HTMLDivElement>(null);
    const measurerRef = useRef<HTMLDivElement>(null);

    // 使用 useRef 来持久化缓存，避免在重渲染时丢失
    const paginationCache = useRef<Record<string, string[]>>({});
    const currentContentRef = useRef<{ content: string[], currentPage: number }>({ content: [], currentPage: 1 });

    // 使用 useMemo 创建稳定的内容标识符，避免对象引用变化导致的重新渲染
    const stableContentId = useMemo(() => {
        if (!content) return null;
        // 创建一个基于内容的稳定标识符
        return `${content.chapter_id}-${content.content.length}-${JSON.stringify(content.content.map(s => s.content.length))}`;
    }, [content]);

    // 同步当前状态到 ref，避免闭包问题
    useEffect(() => {
        if (paginatedContent.length === 0) return;
        currentContentRef.current = { content: paginatedContent, currentPage };
    }, [paginatedContent, currentPage]);

    //调用分页分算， 缓存分页结果
    useEffect(() => {
        let isMounted = true;
        let timerId: NodeJS.Timeout | null = null;
        const container = containerRef.current;

        // 添加更精确的调试 - 立即获取当前状态
        const debugContainerState = () => {
            const currentContainer = containerRef.current;
            return {
                exists: !!currentContainer,
                innerHTML: currentContainer?.innerHTML || '',
                innerHTMLLength: currentContainer?.innerHTML?.length || 0,
                children: currentContainer?.children?.length || 0,
                textContent: currentContainer?.textContent || '',
                textContentLength: currentContainer?.textContent?.length || 0,
                clientWidth: currentContainer?.clientWidth || 0,
                clientHeight: currentContainer?.clientHeight || 0
            };
        };

        const initialState = debugContainerState();

        const performPagination = async () => {
            if (!container || !isMounted) return;

            const { clientWidth, clientHeight } = container;
            const cacheKey = `${chapterId}-${displayMode}-${clientWidth}x${clientHeight}`;

            if (paginationCache.current[cacheKey]) {
                // 使用缓存结果
                const cachedPages = paginationCache.current[cacheKey];
                setPaginatedContent(cachedPages);
                setTotalPages(cachedPages.length);
                setIsPaginating(false);
                return;
            }

            // 保存当前显示的内容，避免重新分页时的空白闪现
            const { content: currentContent, currentPage: currentPageNum } = currentContentRef.current;
            if (currentContent.length > 0 && currentPageNum <= currentContent.length) {
                setPreviousContent(currentContent[currentPageNum - 1] || '');
            }

            // 设置loading状态
            // setIsPaginating(true);

            // 执行分页计算
            timerId = setTimeout(async () => {
                if (!isMounted) return;

                // 直接在这里执行分页逻辑，避免依赖paginate函数
                const pages = await (async () => {
                    if (!containerRef.current || !measurerRef.current || !content) return [];

                    const container = containerRef.current;
                    const measurer = measurerRef.current;
                    const containerHeight = container.clientHeight;

                    // 确保测量器与容器宽度一致
                    measurer.style.width = `${container.clientWidth}px`;
                    measurer.innerHTML = ''; // 清空测量器

                    // 1. 根据 viewMode 生成所有需要渲染的 DOM 元素节点
                    const elementsToPaginate: HTMLElement[] = [];

                    // 添加章节标题作为第一个元素（仅在第一页显示）
                    const titleElement = document.createElement('div');
                    titleElement.className = 'text-center pb-8 mb-6';
                    const titleH2 = document.createElement('h2');
                    titleH2.className = 'text-2xl font-bold text-foreground font-serif';
                    titleH2.textContent = chapterTitle;
                    titleElement.appendChild(titleH2);
                    elementsToPaginate.push(titleElement);

                    content.content.forEach(section => {
                        const sectionWrapper = document.createElement('div');
                        sectionWrapper.className = 'pb-4'; // section 之间的间距

                        if (displayMode === 'original' || displayMode === 'both') {
                            const pContent = document.createElement('p');
                            pContent.className = 'text-foreground';
                            pContent.textContent = section.content;
                            sectionWrapper.appendChild(pContent);

                            if (section.quote) {
                                const pQuote = document.createElement('p');
                                pQuote.className = 'text-foreground bg-paper-secondary py-2';
                                pQuote.textContent = section.quote;
                                sectionWrapper.appendChild(pQuote);
                            }
                        }

                        if (displayMode === 'translation' || displayMode === 'both') {
                            if (section.translation) {
                                const pTranslation = document.createElement('p');
                                pTranslation.className = 'text-blue-700 mt-1';
                                pTranslation.textContent = section.translation;
                                sectionWrapper.appendChild(pTranslation);
                            }
                        }
                        elementsToPaginate.push(sectionWrapper);
                    });

                    // 简化的按段落分页辅助函数 - 保持HTML结构完整
                    const paginateElementByParagraph = (element: HTMLElement): HTMLElement[] => {
                        const elements: HTMLElement[] = [];
                        const tempMeasurer = measurer.cloneNode(false) as HTMLDivElement;
                        tempMeasurer.style.position = 'absolute';
                        tempMeasurer.style.top = '-9999px';
                        tempMeasurer.style.width = `${container.clientWidth}px`;
                        tempMeasurer.className = measurer.className;
                        document.body.appendChild(tempMeasurer);

                        try {
                            // 获取元素内的所有直接子元素（通常是p标签）
                            const childElements = Array.from(element.children) as HTMLElement[];

                            if (childElements.length === 0) {
                                // 如果没有子元素，直接返回原元素
                                elements.push(element.cloneNode(true) as HTMLElement);
                                return elements;
                            }

                            let currentPageElements: HTMLElement[] = [];

                            for (const child of childElements) {
                                // 测试添加这个子元素后是否会超出页面高度
                                const testContainer = element.cloneNode(false) as HTMLElement;

                                // 添加当前页面已有的元素
                                for (const existingEl of currentPageElements) {
                                    testContainer.appendChild(existingEl.cloneNode(true));
                                }

                                // 添加当前测试的子元素
                                testContainer.appendChild(child.cloneNode(true));

                                tempMeasurer.innerHTML = '';
                                tempMeasurer.appendChild(testContainer);

                                if (tempMeasurer.scrollHeight <= containerHeight) {
                                    // 可以放入当前页
                                    currentPageElements.push(child.cloneNode(true) as HTMLElement);
                                } else {
                                    // 放不下，需要开始新页
                                    if (currentPageElements.length > 0) {
                                        // 保存当前页
                                        const pageContainer = element.cloneNode(false) as HTMLElement;
                                        for (const el of currentPageElements) {
                                            pageContainer.appendChild(el);
                                        }
                                        elements.push(pageContainer);
                                        currentPageElements = [];
                                    }

                                    // 检查单个子元素是否也超出页面高度
                                    const singleTestContainer = element.cloneNode(false) as HTMLElement;
                                    singleTestContainer.appendChild(child.cloneNode(true));
                                    tempMeasurer.innerHTML = '';
                                    tempMeasurer.appendChild(singleTestContainer);

                                    if (tempMeasurer.scrollHeight <= containerHeight) {
                                        // 单个元素可以放入，开始新页
                                        currentPageElements.push(child.cloneNode(true) as HTMLElement);
                                    } else {
                                        // 单个元素也太大，需要按文本分割
                                        const splitElements = splitLongText(child, element, tempMeasurer, containerHeight);
                                        elements.push(...splitElements);
                                    }
                                }
                            }

                            // 添加最后一页的剩余元素
                            if (currentPageElements.length > 0) {
                                const pageContainer = element.cloneNode(false) as HTMLElement;
                                for (const el of currentPageElements) {
                                    pageContainer.appendChild(el);
                                }
                                elements.push(pageContainer);
                            }

                        } finally {
                            document.body.removeChild(tempMeasurer);
                        }

                        return elements;
                    };

                    // 辅助函数：分割过长的文本元素
                    const splitLongText = (element: HTMLElement, containerTemplate: HTMLElement, measurer: HTMLElement, maxHeight: number): HTMLElement[] => {
                        const results: HTMLElement[] = [];
                        const text = element.textContent || '';
                        const words = text.split('');

                        let currentText = '';

                        let prevHtml = ''
                        let prevScrollHeight = 0
                        let prevClientHeight = 0
                        let prevClientWidth = 0
                        for (let i = 0; i < words.length; i++) {
                            const testText = currentText + words[i];

                            // 创建测试元素
                            const testElement = element.cloneNode(false) as HTMLElement;
                            testElement.textContent = testText;

                            const testContainer = containerTemplate.cloneNode(false) as HTMLElement;
                            testContainer.appendChild(testElement);

                            measurer.innerHTML = '';
                            measurer.appendChild(testContainer);


                            if (measurer.scrollHeight <= maxHeight) {
                                currentText = testText;
                                prevScrollHeight = measurer.scrollHeight;
                                prevHtml = measurer.outerHTML;
                                prevClientHeight = measurer.clientHeight
                                prevClientWidth = measurer.clientWidth
                            } else {
                                // 当前文本放不下，保存之前的文本
                                if (currentText) {
                                    const pageElement = element.cloneNode(false) as HTMLElement;
                                    pageElement.textContent = currentText;
                                    const pageContainer = containerTemplate.cloneNode(false) as HTMLElement;
                                    pageContainer.appendChild(pageElement);
                                    results.push(pageContainer);
                                }
                                currentText = words[i];
                            }
                        }

                        // 添加最后的文本
                        if (currentText) {
                            const pageElement = element.cloneNode(false) as HTMLElement;
                            pageElement.textContent = currentText;
                            const pageContainer = containerTemplate.cloneNode(false) as HTMLElement;
                            pageContainer.appendChild(pageElement);
                            results.push(pageContainer);
                        }
                        return results;
                    };

                    // 2. 逐个元素添入测量器，判断是否超出一页
                    const pages: string[] = [];
                    for (const el of elementsToPaginate) {
                        measurer.appendChild(el);
                        // 如果添加当前元素后超高
                        if (measurer.scrollHeight > containerHeight) {
                            // 从测量器中移除导致超高的元素
                            measurer.removeChild(el);

                            // 检查当前元素本身是否就超过页面高度
                            const tempMeasurer = document.createElement('div');
                            tempMeasurer.style.position = 'absolute';
                            tempMeasurer.style.top = '-9999px';
                            tempMeasurer.style.width = `${container.clientWidth}px`;
                            tempMeasurer.className = measurer.className;
                            document.body.appendChild(tempMeasurer);
                            tempMeasurer.appendChild(el.cloneNode(true));

                            const elementExceedsPage = tempMeasurer.scrollHeight > containerHeight;
                            document.body.removeChild(tempMeasurer);

                            if (elementExceedsPage) {
                                // 元素本身超高，需要按字符分页
                                // 先保存当前页面内容（如果有的话）
                                if (measurer.innerHTML.trim()) {
                                    pages.push(measurer.innerHTML);
                                    measurer.innerHTML = '';
                                }

                                // 按段落分页处理超长元素
                                const paginatedElements = paginateElementByParagraph(el);
                                for (const paginatedEl of paginatedElements) {
                                    pages.push(paginatedEl.outerHTML);
                                }
                            } else {
                                // 元素本身不超高，按原逻辑处理
                                // 此时测量器的内容就是完整的一页
                                pages.push(measurer.innerHTML);
                                // 清空测量器，并将刚导致超高的元素作为下一页的第一个元素
                                measurer.innerHTML = '';
                                measurer.appendChild(el);
                            }
                        }
                    }

                    // 3. 添加最后一页的剩余内容
                    if (measurer.innerHTML) {
                        pages.push(measurer.innerHTML);
                    }

                    return pages;
                })();

                if (pages && isMounted) {
                    setPaginatedContent(pages);
                    setTotalPages(pages.length);
                    paginationCache.current[cacheKey] = pages;
                    setPreviousContent(''); // 清空临时内容
                    setIsPaginating(false);
                }
            }, 50);
        };

        performPagination();

        // 使用防抖来避免 ResizeObserver 触发过于频繁
        let resizeTimer: NodeJS.Timeout | null = null;
        const resizeObserver = new ResizeObserver((entries) => {
            // 清除之前的定时器
            if (resizeTimer) {
                clearTimeout(resizeTimer);
            }

            // 检查尺寸变化是否显著（避免微小变化触发重新分页）
            const entry = entries[0];
            if (entry && container) {
                // 使用与分页逻辑相同的尺寸计算方式
                const { clientWidth, clientHeight } = container;
                const currentKey = `${chapterId}-${displayMode}-${clientWidth}x${clientHeight}`;

                // 如果已经有这个尺寸的缓存，就不需要重新分页
                if (paginationCache.current[currentKey]) {
                    return;
                }
                // 使用防抖延迟执行
                resizeTimer = setTimeout(() => {
                    if (isMounted) {
                        performPagination();
                    }
                }, 100);
            }
        });

        if (container) {
            resizeObserver.observe(container);
        }

        return () => {
            isMounted = false;
            resizeObserver.disconnect();
            if (timerId) {
                clearTimeout(timerId);
            }
            if (resizeTimer) {
                clearTimeout(resizeTimer);
            }
        };
    }, [chapterId, displayMode, stableContentId, chapterTitle, setTotalPages, content]); 

    //点击屏幕左右翻页, 待优化, 中间部分需要改造为popover
    const handleContainerClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (isPaginating) return;
        const { clientWidth } = e.currentTarget;
        const clickX = e.clientX - e.currentTarget.getBoundingClientRect().left;

        if (clickX > clientWidth / 3 * 2) {
            onGoToNext();
        }
        else if (clickX < clientWidth / 3) {
            onGoToPrev();
        }
        else {
            //弹出顶部导航栏和底部组件
            onToggleBars();
        }
    };

    return (
        <div className="w-full h-full flex flex-col relative font-serif">
            <div ref={containerRef} className="flex-grow w-full p-6 leading-relaxed text-base text-justify overflow-hidden cursor-pointer select-none" style={{ height: '100%' }} onClick={handleContainerClick}>
                    {isPaginating && previousContent ? (
                        // 分页计算期间，显示之前的内容以保持布局稳定
                        <div className="w-full h-full opacity-75 overflow-hidden" dangerouslySetInnerHTML={{ __html: previousContent }} />
                    ) : isPaginating ? (
                        // 如果没有之前的内容，显示加载状态，但保持相同的容器结构
                        <div className="w-full h-full flex items-center justify-center">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4 mx-auto"></div>
                                <p className="text-foreground text-base font-medium mb-2">正在加载章节内容</p>
                            </div>
                        </div>
                    ) : (
                        <div className="w-full h-full overflow-hidden" dangerouslySetInnerHTML={{
                            __html: paginatedContent[currentPage - 1] || ''
                        }} />
                    )}
            </div>
            <div ref={measurerRef} className="absolute top-0 font-serif left-0 opacity-0 pointer-events-none -z-50 p-6 leading-relaxed text-base text-justify" style={{ position: 'fixed', top: '-9999px', left: '-9999px', visibility: 'hidden' }} aria-hidden="true" />
        </div>
    );
}