"use client"

import React, { memo } from "react"
import { CaseResponse } from "@/types/case"
import dayjs from "dayjs"
import { UserIcon } from "@/components/icons/basic"
import { Badge } from "@/components/ui/badge"

interface CaseCardProps {
  profile: CaseResponse
  onClick?: (profile: CaseResponse) => void
  className?: string
}

const CaseCard: React.FC<CaseCardProps> = ({ 
  profile, 
  onClick,
  className = ""
}) => {
  // 统一的样式主题
  const getUnifiedTheme = () => {
    return {
      backgroundImage: "url('/images/case_face.png')",
      border: 'border-gray-200/40',
      name: 'text-gray-800',
      info: 'text-gray-600',
      avatar: 'from-gray-200 to-slate-300',
      avatarText: 'text-gray-800',
      tag: 'bg-slate-100 text-slate-600 text-[10px]'
    }
  }
  
  // 格式化性别显示
  const getGenderColor = (gender: string) => {
    return gender === 'male' || gender === '男' ? 'text-blue-600' : 
           gender === 'female' || gender === '女' ? 'text-pink-600' : 'text-primary'
  }

  const theme = getUnifiedTheme()

  return (
    <div 
      data-item-id={profile.case_id}
      className={`
        flex flex-col group relative overflow-hidden rounded-2xl ${theme.border} items-center justify-center
        bg-gradient-to-br from-[#f1ebe4] via-[#f7f4edff] to-[#E8DFD3]
        will-change-transform transition-transform duration-200 ease-out aspect-[3/4]
        select-none no-callout no-drag
        ${onClick ? 'cursor-pointer active:scale-[0.98] touch-manipulation' : ''}
        ${className}
      `}
      onClick={() => onClick?.(profile)}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
      // onKeyDown={handleKeyDown}
      aria-label={onClick ? `查看${profile.user_name}的档案` : undefined}
    >
      
      {/* 主要内容区域 - 优化3列布局间距 */}
      <div className="relative flex flex-col items-center justify-center px-2 py-8 space-y-2">
        <div className="flex flex-col items-center justify-center space-y-1">
        {/* 头像和名称 - 3列布局优化 */}
        <div className="flex items-center justify-center relative w-full">
          {/* 名称 - 突出显示，支持自动换行 */}
          <div className={`
            text-base font-bold ${theme.name} text-center leading-tight 
            tracking-wide font-sans break-words w-full px-2
          `}>
            {profile.user_name}
          </div>
        </div>

        {/* 基本信息 - 紧凑布局 */}
        <div className="space-y-1 text-center flex">
          <UserIcon size="xs" className={`${getGenderColor(profile.gender)} flex-shrink-0`} />
          <p className={`text-[10px] ${theme.info} leading-relaxed`}>
            {dayjs(profile.birth_time_solar).format('YYYY-MM-DD')}
          </p>
        </div>
        </div>

        {/* 标签区域 - 3列布局优化 */}
        {profile.tags && profile.tags.length > 0 && (
          <div className="absolute bottom-1 left-0 right-0 flex flex-wrap justify-center gap-1 min-h-[1.25rem]">
            {profile.tags.slice(0, 2).map((tag, index) => (
              <Badge key={index} className={`${theme.tag}`}>
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>

    </div>
  )
}

// 使用 memo 避免不必要的重新渲染
const MemoizedCaseCard = memo(CaseCard, (prevProps, nextProps) => {
  // 只有关键属性变化时才重新渲染
  return (
    prevProps.profile.case_id === nextProps.profile.case_id &&
    prevProps.profile.user_name === nextProps.profile.user_name &&
    prevProps.profile.birth_time_solar === nextProps.profile.birth_time_solar &&
    prevProps.onClick === nextProps.onClick
  )
})

MemoizedCaseCard.displayName = 'CaseCard'
export default MemoizedCaseCard