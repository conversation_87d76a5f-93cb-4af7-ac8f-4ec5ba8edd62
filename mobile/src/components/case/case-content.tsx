
"use client"

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import MobileHeader from '@/components/layout/mobile/head-nav'
import ZiweiResultContent from '@/components/ziwei/ziwei-divinate'
import DeleteCaseDialog from '@/components/case/delete-case-dialog'
import { Pencil, Trash2 } from 'lucide-react'
import { BirthData } from '@/types/user'
import { BirthInfoForm } from '@/components/birth-info-form'
import { ZiweiDivinateRequest, BirthTimeInfo } from '@/types/divinate'
import { useCreateCase, useUpdateCase } from '@/hooks/use-api'
import { saveArrayToSessionStorage, hasValueInSessionStorage } from "@/lib/storage"
import { getBirthTimeInfo, getZiweiDivinateRequest } from "@/lib/astro-utils"
import { CaseType, RelationType } from '@/types/enums'

import {
    Dialog,
    DialogContent,
    DialogTitle,
} from "@/components/ui/dialog"

// 定义组件的props接口
interface CaseContentProps {
    birthData: BirthData
    submitID: string
    isSaveCaseDocument: boolean
    currentCaseId: string | null
}

export default function CaseContent({
    birthData,
    submitID,
    isSaveCaseDocument,
    currentCaseId
}: CaseContentProps) {
    const router = useRouter()
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false)
    const [isEditDialogOpen, setIsEditDialogOpen] = useState<boolean>(false)

    const hasCreatedCase = useRef<boolean>(false) // 添加保存状态标记, 防止一次挂载多次保存
    const isProcessing = useRef<boolean>(false) // 防止并发保存/更新操作
    const [shouldUpdateCase, setShouldUpdateCase] = useState<boolean>(false) // 是否需要更新案例
    const [shouldAutoSave, setShouldAutoSave] = useState<boolean>(isSaveCaseDocument)

    const [caseId, setCaseId] = useState<string | null>(currentCaseId)
    const [currentBirthData, setCurrentBirthData] = useState<BirthData>(birthData)
    const [birthTimeInfo, setBirthTimeInfo] = useState<BirthTimeInfo | null>(null)
    const [ziweiDivinateRequest, setZiweiDivinateRequest] = useState<ZiweiDivinateRequest | null>(null)

    const createCaseMutation = useCreateCase()
    const updateCaseMutation = useUpdateCase()
    const [error, setError] = useState<string | null>(null)

    // 使用 ref 存储最新的 mutations，避免依赖循环
    const createCaseMutationRef = useRef(createCaseMutation)
    const updateCaseMutationRef = useRef(updateCaseMutation)
    createCaseMutationRef.current = createCaseMutation
    updateCaseMutationRef.current = updateCaseMutation

    // 初始化紫微斗数请求数据和处理自动保存逻辑
    useEffect(() => {
        const initializeZiweiRequest = async () => {
            try {
                // 验证必要参数
                if (!currentBirthData.name || !currentBirthData.gender || !currentBirthData.birthTime || !currentBirthData.birthplace || !currentBirthData.relationship) {
                    setError('缺少必要的出生信息参数')
                    return
                }
                
                // 计算出生时间信息
                const birthTimeInfoResult = getBirthTimeInfo(currentBirthData)
                
                if (!birthTimeInfoResult) {
                    setError('获取出生时间信息失败')
                    return
                }

                // 使用 setTimeout 让出主线程，避免UI卡死
                const request = await new Promise<ZiweiDivinateRequest>((resolve, reject) => {
                    setTimeout(() => {
                        try {
                            // 然后生成ZiweiDivinateRequest
                            const ziweiRequest = getZiweiDivinateRequest(currentBirthData.name, currentBirthData.gender, birthTimeInfoResult)
                            if (!ziweiRequest) {
                                reject(new Error('计算失败，请检查出生日期是否正确'))
                                return
                            }
                            resolve(ziweiRequest)
                        } catch (error) {
                            reject(error)
                        }
                    }, 0)
                })

                // 更新状态
                setBirthTimeInfo(birthTimeInfoResult)
                setZiweiDivinateRequest(request)

                // 处理自动保存逻辑 - 使用计算出的最新值，而不是依赖状态更新
                if (shouldAutoSave && !isProcessing.current) {
                    isProcessing.current = true // 设置处理标记，防止重复调用
                    
                    const performSave = async () => {
                        try {
                            if (currentCaseId) {
                                // 更新已有案例
                                await updateCase(currentBirthData, birthTimeInfoResult)
                            } else {
                                // 保存新案例
                                await saveCase(submitID, currentBirthData, birthTimeInfoResult)
                            }
                        } finally {
                            isProcessing.current = false // 无论成功失败都要重置标记
                        }
                    }

                    performSave()
                }

            } catch (err) {
                console.error('生成紫微斗数请求失败:', err)
                setError(err instanceof Error ? err.message : '计算失败，请稍后再试')
            }
        }

        // 更新案例的函数
        const updateCase = async (birthData: BirthData, birthTimeInfo: BirthTimeInfo) => {
            if (!shouldUpdateCase) {
                return
            }

            if (!currentCaseId) {
                console.error('更新案例失败：没有有效的案例ID')
                return
            }

            try {
                await updateCaseMutationRef.current.trigger({
                    caseId: currentCaseId,
                    updates: {
                        user_name: birthData.name,
                        gender: birthData.gender,
                        birth_time_solar: birthTimeInfo.solarTime,
                        birth_time_lunar: birthTimeInfo.lunarTime,
                        bazi_year: birthTimeInfo.bazi.year,
                        bazi_month: birthTimeInfo.bazi.month,
                        bazi_day: birthTimeInfo.bazi.day,
                        bazi_time: birthTimeInfo.bazi.time,
                        birth_place: birthData.birthplace,
                        relation_type: birthData.relationship as RelationType
                    }
                })

                console.log('案例更新成功:')
                setShouldUpdateCase(false)
            } catch (err) {
                console.error('更新案例失败:', err)
            }
        }

        // 保存新建案例的函数
        const saveCase = async (submitID: string, birthData: BirthData, birthTimeInfo: BirthTimeInfo) => {
            // 多重检查防止重复保存
            if (hasCreatedCase.current || hasValueInSessionStorage('savedSubmitIds', submitID)) {
                console.log('当前提交案例已保存过，跳过保存')
                return
            }

            try {
                const newCaseId = await createCaseMutationRef.current.trigger({
                    birthData: birthData,
                    lunarTime: birthTimeInfo.lunarTime,
                    bazi: birthTimeInfo.bazi,
                    caseType: CaseType.ZIWEI
                })

                if (newCaseId) {
                    setCaseId(newCaseId)
                    saveArrayToSessionStorage('savedSubmitIds', submitID)
                    hasCreatedCase.current = true
                    console.log('案例保存成功:')
                }
            } catch (err) {
                console.error('保存案例失败:', err)
            }
        }

        initializeZiweiRequest()

        // 清理函数：组件卸载时重置处理标记
        return () => {
            isProcessing.current = false
        }
    }, [currentBirthData, shouldAutoSave, shouldUpdateCase, submitID, currentCaseId])

    const handleDeleteSuccess = () => {
        // 删除成功后返回前一个页面
        router.back()
    }

    // 创建基于权限和状态的菜单项
    const createPermissionBasedMenu = useCallback(() => {
        const effectiveCaseId = currentCaseId || caseId
        const hasValidCaseId = !!effectiveCaseId

        const handleDelete = () => {
            if (!effectiveCaseId) return
            setIsDeleteDialogOpen(true)
        }

        const handleEdit = () => {
            setIsEditDialogOpen(true)
        }
        return [
            {
                id: 'edit',
                label: '编辑',
                icon: <Pencil size={16} />,
                onClick: handleEdit,
                disabled: !currentBirthData,
            },
            {
                id: 'delete',
                label: '删除',
                icon: <Trash2 size={16} />,
                onClick: handleDelete,
                disabled: !hasValidCaseId, // 当没有有效的案例ID时禁用删除按钮
            }
        ]
    }, [currentCaseId, currentBirthData, caseId])

    const handleEditFormSubmit = async (newBirthData: BirthData, isSaveCaseDocument: boolean, submitId: string) => {
        setIsEditDialogOpen(false)
        // 设置是否需要自动保存
        setShouldAutoSave(isSaveCaseDocument)
        if (isSaveCaseDocument) {
            setShouldUpdateCase(true)
        }

        // 更新当前页面的birthData - 这会触发useEffect重新计算ZiweiDivinateRequest
        setCurrentBirthData(newBirthData)
    }

    console.log("shouldAutoSave", shouldAutoSave)
    // 错误状态显示
    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <Card className="shadow-lg mb-8">
                    <CardContent>
                        <div className="flex flex-col items-center justify-center h-[400px] text-center">
                            <p className="text-red-500 mb-4">{error}</p>
                            <button
                                onClick={() => {
                                    router.back()
                                }}
                                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
                            >
                                返回
                            </button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    // 加载状态显示
    if (!ziweiDivinateRequest) {
        return (
            <div className="container mx-auto px-4 py-8">
                <Card className="shadow-lg mb-8">
                    <CardContent>
                        <div className="flex flex-col items-center justify-center h-[400px] text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
                            <p className="text-gray-500">正在计算命盘数据...</p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="min-h-full w-full overflow-y-scroll">
            {/* 头部导航 */}
            <MobileHeader
                title={currentBirthData.name}
                showDropdownMenu={true}
                dropdownMenuItems={createPermissionBasedMenu()}
            />

            {/* 内容区域 */}
            <div className="flex flex-col w-full px-2 py-1 space-y-6">
                {/* 案例ID信息 */}
                <Card className="flex flex-col w-full py-1">
                    <ZiweiResultContent
                        ziweiRequest={ziweiDivinateRequest!}
                    />
                </Card>

                {/* 紫微斗数命盘解析 */}
                <Card>
                    <CardHeader>
                        <CardTitle>紫微斗数命盘解析</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-center py-8 text-gray-500">
                            <p>命盘解析功能开发中...</p>
                            <p className="text-sm mt-2">基于案例ID: {currentCaseId || caseId || '暂无'}</p>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* 删除确认对话框 */}
            <DeleteCaseDialog
                caseId={currentCaseId || caseId}
                caseName={currentBirthData.name}
                isOpen={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
                onSuccess={handleDeleteSuccess}
            />

            {/* 编辑确认对话框 */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogTitle hidden={true}></DialogTitle>
                <DialogContent className="bg-none p-0 border-none shadow-none rounded-2xl max-w-[320px]">
                    <BirthInfoForm
                        title="编辑案例"
                        submitLabel='提交'
                        onSubmit={handleEditFormSubmit}
                        isLoading={false}
                        defaultBirthData={currentBirthData}
                        defaultIsSaveCaseDocument={shouldAutoSave}
                    />
                </DialogContent>
            </Dialog>
        </div>
    )
}