"use client"

import React, { useState, useCallback } from "react"
import { Toast } from 'antd-mobile'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useDeleteCase } from "@/hooks/use-api"

interface DeleteCaseDialogProps {
  caseId: string | null
  caseName: string
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
  onError?: (error: string) => void
}

export default function DeleteCaseDialog({
  caseId,
  caseName,
  isOpen,
  onOpenChange,
  onSuccess,
  onError
}: DeleteCaseDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  
  // 删除案例的 mutation
  const { trigger: deleteCase, isMutating: isDeletingCase } = useDeleteCase()

  const handleDelete = useCallback(async () => {
    if (!caseId) return

    setIsDeleting(true)
    onOpenChange(false)

    try {
      console.log('开始删除案例:', caseName, caseId)

      // 调用删除API
      const result = await deleteCase({ caseId: caseId })

      if (result) {
        // 删除成功
        Toast.show({
          content: `案例 "${caseName}" 删除成功`,
          icon: 'success',
          duration: 2000,
        })

        // 调用成功回调
        onSuccess?.()
      } else {
        // 删除失败
        const errorMsg = '删除失败，请稍后重试'
        Toast.show({
          content: errorMsg,
          icon: 'fail',
          duration: 2000,
        })
        onError?.(errorMsg)
      }
    } catch (error) {
      console.error('删除案例失败:', error)
      const errorMsg = error instanceof Error ? error.message : '删除失败，请稍后重试'
      Toast.show({
        content: errorMsg,
        icon: 'fail',
        duration: 2000,
      })
      onError?.(errorMsg)
    } finally {
      setIsDeleting(false)
    }
  }, [caseId, caseName, deleteCase, onOpenChange, onSuccess, onError])

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <span>确认删除？</span>
          </AlertDialogTitle>
          <AlertDialogDescription>
            <p className='text-red-600'>注意，案例删除后不可恢复</p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting || isDeletingCase}
            className="w-full sm:w-auto bg-primary hover:bg-primary/70 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDeleting || isDeletingCase ? '删除中...' : '删除案例'}
          </AlertDialogAction>
          <AlertDialogCancel className="w-full sm:w-auto">
            取消
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
