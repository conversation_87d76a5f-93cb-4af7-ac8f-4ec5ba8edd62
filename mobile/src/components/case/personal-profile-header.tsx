"use client"

import React from "react"
import { PersonalProfile } from "@/types/case"
import { Mars, Venus } from "lucide-react"
import { UserIcon } from "@/components/icons/basic"

interface PersonalProfileHeaderProps {
  profile: PersonalProfile
  onClick?: () => void
}

const PersonalProfileHeader: React.FC<PersonalProfileHeaderProps> = ({
  profile,
  onClick
}) => {
  // 格式化出生时间显示
  const formatBirthTime = (solarTime: string) => {
    const solarDate = new Date(solarTime).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
    return `${solarDate}`
  }

  // 获取性别对应的emoji
  const getGenderEmoji = (gender: string) => {
    switch (gender) {
      case '男': return <Mars className="w-4 h-4" />
      case '女': return <Venus className="w-4 h-4" />
      case 'male': return <Mars className="w-4 h-4" />
      case 'female': return <Venus className="w-4 h-4" />
      default: return ''
    }
  }

  // 默认今日运势
  const defaultFortune = "今日适宜学习，宜静心思考，财运平稳"
  const bg_url = 'https://images.unsplash.com/photo-1518655048521-f130df041f66?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80'
  return (
    <div
      className="case-my-card bg-linear-to-br from-[#f6f2eaff] to-[#E8DFD3] bg-cover bg-center"
      //className="case-my-card bg-linear-to-br from-[#FCFAF8] to-[#E8DFD3] bg-cover bg-center"
      // style={{backgroundImage: `linear-gradient(rgba(246,242,234,1.00), rgba(246,242,234,0.7)), url(${bg_url})`}}
      onClick={onClick}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          onClick()
        }
      } : undefined}
    >
      {/* 头部信息 */}
      <div className="flex items-start justify-between my-2">
        <div className="flex-1">
          <div className="flex items-center gap-2 mt-1 mb-2">
            <h2 className="text-lg font-bold text-foreground text-primary tracking-wide">
              {profile.user_name}
            </h2>
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-2 text-foreground/70">
              <UserIcon size="sm" />
              <span className="text-sm">
                {profile.gender === 'female' ? '女' : '男'}
              </span>
              <span>
              <p className="text-sm text-foreground/70 leading-relaxed">
                {formatBirthTime(profile.birth_time_solar.toString())}
              </p>
              </span>
            </div>
            <p className="text-sm text-foreground/70 leading-relaxed">
              {profile.bazi_year} {profile.bazi_month} {profile.bazi_day} {profile.bazi_time}
            </p>
          </div>
        </div>
      </div>

      {/* 标签 */}
      {profile.tags && profile.tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {profile.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100/70 text-emerald-700 border border-emerald-200/50"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      {/* 今日运势 */}
      {/* <div className="pt-4 border-t border-emerald-200/50">
        <div className="flex items-start gap-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-br from-yellow-100 to-amber-100 flex-shrink-0">
            <span className="text-lg">✨</span>
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-semibold text-emerald-800 mb-1">
              今日运势
            </h3>
            <p className="text-sm text-emerald-700/90 leading-relaxed">
              {profile.today_fortune || defaultFortune}
            </p>
          </div>
        </div>
      </div> */}

      {/* 点击提示 */}
      {onClick && (
        <div className="absolute bottom-4 right-4 opacity-60">
          <svg
            className="w-5 h-5 text-emerald-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
      )}
    </div>
  )
}

export default PersonalProfileHeader