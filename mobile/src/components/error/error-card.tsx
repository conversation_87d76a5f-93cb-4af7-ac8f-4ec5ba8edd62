'use client';

type ErrorMessageProps = {
  message: string;
  onRetry?: () => void;
};

export function SectionError({
  message,
  onRetry,
}: ErrorMessageProps) {
  return (
    <div className="w-full rounded-2xl p-4 text-center">
      <p className="text-sm text-muted-foreground">{message ?? '该模块加载失败'}</p>
      {onRetry && (
        <button
          className="mt-3 rounded-xl px-3 py-2 bg-primary text-primary-foreground"
          onClick={onRetry}
        >
          重试
        </button>
      )}
    </div>
  );
}

export function PageError({
  message,
  onRetry,
}: ErrorMessageProps) {
  return (
      <div className="flex flex-col items-center justify-center py-20 text-center">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 4h.01M5 12h.01M19 12h.01" />
          </svg>
        </div>
        <p className="text-gray-500 text-sm">{message ?? '该模块加载失败'}</p>
        {onRetry && (
        <button 
          onClick={onRetry} 
          className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm hover:bg-primary-foreground hover:text-muted transition-colors"
        >
          重新加载
        </button>
        )}
      </div>
  )
}
