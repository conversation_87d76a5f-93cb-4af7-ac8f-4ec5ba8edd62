"use client"

import { usePathname } from 'next/navigation'
import { Plus } from "lucide-react"
import { BaziLightIcon, AstroLightIcon, MeihuaLightIcon, UserLightIcon } from "@/components/icons/basic"
import { useBottomNavActive } from '@/hooks/use-bottom-nav-active'
import { cn } from "@/lib/utils"

// 不显示底部导航栏的页面路径
const HIDDEN_PATHS = [
  '/login',
  '/register',
  '/book/detail',
  // 添加更多需要隐藏的路径
]

const navItems: Array<{
  label: string
  icon: any
  icon_size: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | number
  key: string
  isSpecial?: boolean
}> = [
  {
    label: '八字',
    icon: BaziLightIcon,
    icon_size: 'md',
    key: 'bazi'
  },
  {
    label: '紫微',
    icon: AstroLightIcon,
    icon_size: 'md',
    key: 'ziwei'
  },
  {
    label: '测',
    icon: Plus,
    icon_size: 'md',
    key: 'divinate',
    isSpecial: true
  },
  {
    label: '梅花',
    icon: MeihuaLightIcon, 
    icon_size: 'md',
    key: 'meihua'
  },
  {
    label: '我的',
    icon: UserLightIcon,
    icon_size: 24,
    key: 'user'
  }
]

export default function MobileBottomNavigation() {
  const pathname = usePathname()
  const { activeNav, currentCategory, handleNavClick } = useBottomNavActive();

  // 判断当前页面是否需要隐藏底部导航栏
  const shouldHide = HIDDEN_PATHS.some(path => 
    pathname === path || pathname.startsWith(path)
  )

  if (shouldHide) {
    return <div className="fixed bottom-0 left-0 right-0 z-50 bg-background safe-area-inset-bottom"></div>
  }

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-border/40 mobile-bottom-nav fixed-optimized">
      <div className="flex items-center justify-around px-2 py-1">
        {navItems.map((item) => {
          const isActive = (activeNav === item.key || (currentCategory == item.key && activeNav === 'divinate')) 
          const Icon = item.icon
          
          if (item.isSpecial) {
            // 特殊的测算按钮
            return (
              <button
                key={item.key}
                onClick={() => handleNavClick(item.key as any)}
                className={cn(
                  "flex flex-col items-center min-w-0 flex-1 py-1 px-1 transition-all duration-200",
                  "active:scale-120",
                  isActive ? "scale-120" : ""
                )}
                aria-label={item.label}
              >
                <div className="flex items-center justify-center h-10 w-10 rounded-full bg-primary text-primary-foreground shadow-lg transition-all duration-200 hover:scale-105">
                  <span className="text-base font-bold">{item.label}</span>
                </div>
              </button>
            )
          }

          return (
            <button
              key={item.key}
              onClick={() => handleNavClick(item.key as any)}
              className={cn(
                "flex flex-col items-center min-w-0 flex-1 py-1 px-1 transition-all duration-200 rounded-lg",
                "active:scale-95 hover:text-primary group",
                isActive ? "bg-muted/50" : "hover:text-primary"
              )}
              aria-label={item.label}
            >
              <div className={cn(
                "p-0 rounded-lg transition-colors",
                isActive ? "bg-primary/10" : ""
              )}>
                <Icon size={item.icon_size} className={cn(
                  "transition-colors mb-1",
                  isActive ? "text-primary" : "text-muted-foreground group-hover:text-primary"
                )} />
              </div>
              <span className={cn(
                "text-xs font-medium transition-colors truncate",
                isActive ? "text-primary" : "text-muted-foreground group-hover:text-primary"
              )}>
                {item.label}
              </span>
            </button>
          )
        })}
      </div>
    </nav>
  )
} 