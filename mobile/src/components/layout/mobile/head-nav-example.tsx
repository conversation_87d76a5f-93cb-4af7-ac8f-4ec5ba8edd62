"use client"

import { useAuth } from "@/hooks/use-auth"
import { useRouter } from 'next/navigation'
import { Settings, BookOpen, User, LogOut, Edit, Share, Download } from 'lucide-react'
import MobileNav from './head-nav'

// 示例：八字页面的下拉菜单项
const baziDropdownItems = [
  {
    id: 'settings',
    label: '设置',
    icon: <Settings size={16} />,
    onClick: () => console.log('打开设置'),
    condition: () => true // 始终显示
  },
  {
    id: 'edit',
    label: '编辑',
    icon: <Edit size={16} />,
    onClick: () => console.log('编辑模式'),
    condition: () => true
  },
  {
    id: 'share',
    label: '分享',
    icon: <Share size={16} />,
    onClick: () => console.log('分享结果'),
    condition: () => true
  },
  {
    id: 'download',
    label: '下载',
    icon: <Download size={16} />,
    onClick: () => console.log('下载报告'),
    condition: () => true
  }
];

// 示例：书籍页面的下拉菜单项
const bookDropdownItems = [
  {
    id: 'bookmark',
    label: '收藏',
    icon: <BookOpen size={16} />,
    onClick: () => console.log('收藏书籍'),
    condition: () => true
  },
  {
    id: 'share',
    label: '分享',
    icon: <Share size={16} />,
    onClick: () => console.log('分享书籍'),
    condition: () => true
  },
  {
    id: 'download',
    label: '下载',
    icon: <Download size={16} />,
    onClick: () => console.log('下载书籍'),
    condition: () => true
  }
];

// 示例：用户页面的下拉菜单项
const userDropdownItems = [
  {
    id: 'profile',
    label: '个人资料',
    icon: <User size={16} />,
    onClick: () => console.log('编辑个人资料'),
    condition: () => true
  },
  {
    id: 'settings',
    label: '设置',
    icon: <Settings size={16} />,
    onClick: () => console.log('打开设置'),
    condition: () => true
  },
  {
    id: 'logout',
    label: '退出登录',
    icon: <LogOut size={16} />,
    onClick: () => console.log('退出登录'),
    condition: () => true
  }
];

// 示例：根据用户权限显示不同的菜单项
const createPermissionBasedDropdownItems = (userRole: string) => [
  {
    id: 'view',
    label: '查看',
    icon: <BookOpen size={16} />,
    onClick: () => console.log('查看内容'),
    condition: () => true // 所有用户都可以查看
  },
  {
    id: 'edit',
    label: '编辑',
    icon: <Edit size={16} />,
    onClick: () => console.log('编辑内容'),
    condition: () => userRole === 'admin' || userRole === 'editor' // 只有管理员和编辑者可以编辑
  },
  {
    id: 'delete',
    label: '删除',
    icon: <LogOut size={16} />,
    onClick: () => console.log('删除内容'),
    condition: () => userRole === 'admin' // 只有管理员可以删除
  }
];

// 示例：根据页面状态显示不同的菜单项
const createStateBasedDropdownItems = (isEditing: boolean, hasChanges: boolean) => [
  {
    id: 'save',
    label: '保存',
    icon: <Download size={16} />,
    onClick: () => console.log('保存更改'),
    condition: () => isEditing && hasChanges // 只有在编辑模式且有更改时才显示
  },
  {
    id: 'cancel',
    label: '取消',
    icon: <LogOut size={16} />,
    onClick: () => console.log('取消编辑'),
    condition: () => isEditing // 只有在编辑模式时才显示
  },
  {
    id: 'preview',
    label: '预览',
    icon: <BookOpen size={16} />,
    onClick: () => console.log('预览效果'),
    condition: () => !isEditing // 只有在非编辑模式时才显示
  }
];

// 使用示例组件
export default function MobileNavExample() {
  const { user } = useAuth()
  const router = useRouter()

  // 根据用户角色创建权限相关的菜单项
  const permissionItems = createPermissionBasedDropdownItems(user?.role || 'user')
  
  // 根据页面状态创建状态相关的菜单项
  const stateItems = createStateBasedDropdownItems(false, false)

  return (
    <div className="space-y-4">
      {/* 八字页面示例 */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">八字页面导航</h3>
        <MobileNav
          title="八字测算"
          showDropdownMenu={true}
          dropdownMenuItems={baziDropdownItems}
          onDropdownToggle={(isOpen) => console.log('下拉菜单状态:', isOpen)}
        />
      </div>

      {/* 书籍页面示例 */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">书籍页面导航</h3>
        <MobileNav
          title="经典书籍"
          showDropdownMenu={true}
          dropdownMenuItems={bookDropdownItems}
        />
      </div>

      {/* 用户页面示例 */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">用户页面导航</h3>
        <MobileNav
          title="个人中心"
          showDropdownMenu={true}
          dropdownMenuItems={userDropdownItems}
        />
      </div>

      {/* 权限控制示例 */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">权限控制导航 (当前角色: {user?.role || 'user'})</h3>
        <MobileNav
          title="权限管理"
          showDropdownMenu={true}
          dropdownMenuItems={permissionItems}
        />
      </div>

      {/* 状态控制示例 */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">状态控制导航</h3>
        <MobileNav
          title="内容编辑"
          showDropdownMenu={true}
          dropdownMenuItems={stateItems}
        />
      </div>

      {/* 无下拉菜单示例 */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">无下拉菜单导航</h3>
        <MobileNav
          title="首页"
          showDropdownMenu={false}
        />
      </div>
    </div>
  )
}
