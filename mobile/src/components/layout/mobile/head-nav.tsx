"use client"

import { useAuth } from "@/hooks/use-auth"
import { ChevronLeft, } from "lucide-react"
import { Button } from "@/components/ui/button"
import { usePathname, useRouter } from 'next/navigation'
import { Ellipsis } from 'lucide-react';
import { ReactNode } from 'react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const pageNames: Record<string, string> = {
  '/': '玄学汇',
  '/bazi': '八字',
  '/ziwei': '紫微斗数',
  '/meihua': '梅花易数',
  '/user': '我的',
  '/bazi/divinate': '八字测算',
  '/ziwei/divinate': '紫微测算',
  '/meihua/divinate': '梅花测算',
  '/book': '经典书籍',
  '/course': '课程学习',
  '/cases': '案例解析'
}

// 下拉菜单项接口
interface DropdownMenuItem {
  id: string;
  label: string;
  icon?: ReactNode;
  onClick: () => void;
  condition?: () => boolean; // 控制是否显示的条件函数
  disabled?: boolean;
}

// 组件属性接口
interface MobileNavProps {
  title: string;
  showDropdownMenu?: boolean;
  dropdownMenuItems?: DropdownMenuItem[]; // 新增：下拉菜单项数组
  onDropdownToggle?: (isOpen: boolean) => void; // 新增：下拉菜单开关回调
}

export default function MobileNav({
  title,
  showDropdownMenu = false,
  dropdownMenuItems = [],
  onDropdownToggle
}: MobileNavProps) {
  const pathname = usePathname()
  const router = useRouter()

  const { user, isAuthenticated } = useAuth()

  // 判断是否显示返回按钮
  const shouldShowBackButton = () => {
    const mainPaths = ['/', '/bazi', '/ziwei', '/meihua', '/user']
    return !mainPaths.includes(pathname)
  }

  // 处理返回
  const handleBack = () => {
    if (typeof window !== 'undefined' && window.history.length > 1) {
      router.back()
    } else {
      router.push('/')
    }
  }

  // 过滤显示的下拉菜单项
  const visibleDropdownItems = dropdownMenuItems.filter(item => {
    // 如果没有条件函数，默认显示
    if (!item.condition) return true;
    // 如果有条件函数，执行条件判断
    return item.condition();
  });

  // 处理下拉菜单点击
  const handleDropdownItemClick = (item: DropdownMenuItem) => {
    if (item.disabled) return;
    item.onClick();
  };

  return (
    <div className="flex w-full mobile-top-nav items-center justify-between px-4">
      {/* 左侧：返回按钮或占位 */}
      <div className="flex items-center justify-start w-14">
        {shouldShowBackButton() ? (
          <Button
            variant="ghost"
            onClick={handleBack}
            className="p-0 [&_svg]:size-5 gap-1"
            aria-label="返回"
          >
            <ChevronLeft size={20} color="#324258" />
            <span className="font-normal text-foreground font-serif">返回</span>
          </Button>
        ) : null}
      </div>

      {/* 中间：页面标题 */}
      <div className="flex-1 text-center">
        <h1 className="text-base font-normal text-foreground truncate px-2">
          {title || ''}
        </h1>
      </div>

      {/* 右侧：下拉菜单或占位 */}
      {showDropdownMenu && visibleDropdownItems.length > 0 ? (
        <div className="flex w-14 justify-end">
        <DropdownMenu >
          <DropdownMenuTrigger asChild>
            <Ellipsis size={20} />
          </DropdownMenuTrigger>
          <DropdownMenuContent className="flex flex-col min-w-24" align="start" side="bottom">
            {visibleDropdownItems.map((item) => (
              <DropdownMenuItem 
                key={item.id} 
                className={`text-base ${item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`} 
                onClick={() => handleDropdownItemClick(item)}
                disabled={item.disabled}
              >
                {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                <span>{item.label}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        </div>
      ) : (
        <div className="w-14"></div>
      )}
    </div>
  )
}