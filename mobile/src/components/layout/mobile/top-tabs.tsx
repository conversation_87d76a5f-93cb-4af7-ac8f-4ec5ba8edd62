"use client"
import { useTopTabActive } from '@/hooks/use-top-tab-active'
import { usePathname } from 'next/navigation'
import { Tabs_3 } from '@/components/ui/top-tabs'

const showPaths = [
    '/case/',
    '/course/',
    '/book/',
    '/meihua/',
    '/ziwei/',
    '/bazi/',
]


export default function TopTabs({ currentCategory }: { currentCategory: string }) {
    const { activeTab, handleTabClick } = useTopTabActive(currentCategory);
    const pathname = usePathname()
    const isShow = showPaths.some(path => 
        pathname === path || pathname.endsWith(path)
      )


    const tabs = [
        { id: "case", label: '案例', href: '/case' },
        { id: "course", label: '课程', href: '/course' },
        { id: "book", label: '书籍', href: '/book/list/m' },
    ]
    if (!isShow) {
        return null
    }
    return (
        <Tabs_3 tabs={tabs} activeTab={activeTab} onTabClick={handleTabClick} />
    )
}