"use client"

import { useEffect, useState } from "react"
import { SyntaxErrorDetector } from "@/components/syntax-error-detector"
import { ResourceMonitor, NetworkMonitor } from "@/components/resource-monitor"
import { AuthService } from "@/lib/auth"
import { ThemeProvider } from '@/components/providers/theme-provider'
import { ErrorRuntimeProvider } from '@/components/providers/error-runtime-provider'
import { SWRProvider } from '@/components/providers/swr-provider'
import { HeroUIWrapper } from '@/components/providers/hero-ui-provider'
import 'antd-mobile/es/global';
import { Capacitor } from '@capacitor/core'
import { StatusBar } from '@capacitor/status-bar'

// 认证系统初始化组件 - 全局唯一初始化, 实际上只做刷新token一件事
function AuthInitializer() {
  const [isInitializing, setIsInitializing] = useState(false)

  useEffect(() => {
    const initializeAuth = async () => {
      // 避免重复初始化
      if (AuthService.getInitializationStatus() || isInitializing) {
        return
      }

      setIsInitializing(true)

      try {
        console.log('AuthInitializer: 开始初始化认证系统...')
        await AuthService.init()
        console.log('AuthInitializer: 认证系统初始化完成')
      } catch (error) {
        console.error('AuthInitializer: 认证系统初始化失败:', error)
      } finally {
        setIsInitializing(false)
      }
    }

    // 延迟一帧确保DOM已准备好
    const timer = setTimeout(initializeAuth, 0)

    return () => clearTimeout(timer)
  }, [isInitializing])

  return null
}

const AppSetup = () => {
  useEffect(() => {
    console.log("AppSetup")
    console.log("isNativePlatform:", Capacitor.isNativePlatform())
    if (Capacitor.isNativePlatform()) {
      // 让 WebView 覆盖状态栏区域
      StatusBar.setOverlaysWebView({ overlay: true });
      console.log("set overlaysWebView")
    }
  }, []);

  return null; // 这个组件不渲染任何 UI
};

export function ClientProviders({ children }: { children: React.ReactNode }) {

  return (
    <>
      <ErrorRuntimeProvider>
      <SWRProvider>
        <HeroUIWrapper>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <AppSetup />
            {children}
          </ThemeProvider>
        </HeroUIWrapper>
      </SWRProvider>
      <AuthInitializer />
      <SyntaxErrorDetector />
      </ErrorRuntimeProvider>
      <ResourceMonitor />
      <NetworkMonitor />
      {/* <DebugPanel /> */}
    </>
  )
} 