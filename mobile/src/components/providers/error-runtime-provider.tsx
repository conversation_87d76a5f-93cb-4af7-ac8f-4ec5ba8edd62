// components/providers/ErrorRuntimeProvider.tsx
'use client';

import { useEffect } from 'react';
import { AppError, normalizeError } from '@/lib/error';
import { Toast } from 'antd-mobile';

export function ErrorRuntimeProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      const err = normalizeError(event.error);
      if (err instanceof AppError) {
        Toast.show({ content: err.userMessage ?? '运行时错误' });
      }
    };

    const handleRejection = (event: PromiseRejectionEvent) => {
      const err = normalizeError(event.reason);
      if (err instanceof AppError) {
        Toast.show({ content: err.userMessage ?? '请求失败' });
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleRejection);
    };
  }, []);

  return <>{children}</>;
}
