import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON>eader, DrawerTitle } from '@/components/ui/drawer'
import { DateInput } from '@heroui/date-input'
import { DatePickerView } from 'antd-mobile'
import { ZonedDateTime, parseZonedDateTime, now, getLocalTimeZone } from '@internationalized/date'
import { I18nProvider } from '@react-aria/i18n'

export function DateTimePicker({value, onSelect}: {value: string | null, onSelect: (value: string) => void}) {
    const [isDrawerOpen, setIsDrawerOpen] = React.useState(false)
    // 将各种datetime格式标准化为 "YYYY-MM-DD HH:MM" 格式
    const normalizeDateTime = (dateTimeStr: string): string | null => {
        if (!dateTimeStr) return null;
        
        try {
            // 处理ISO格式: "1995-02-02T07:26:00" -> "1995-02-02 07:26"
            if (dateTimeStr.includes('T')) {
                return dateTimeStr.replace('T', ' ').substring(0, 16);
            }
            
            // 如果已经是标准格式 "YYYY-MM-DD HH:MM"，直接返回
            if (/^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}$/.test(dateTimeStr)) {
                return dateTimeStr;
            }
            
            // 其他格式的处理可以在这里添加
            return dateTimeStr;
        } catch (error) {
            console.warn('Failed to normalize datetime format:', error);
            return null;
        }
    };

    // 统一用 ZonedDateTime 作为单一数据源
    const [date, setDate] = React.useState<ZonedDateTime | null>(() => {
        // 如果有初始值，解析它；否则使用当前时间
        if (value) {
            try {
                const normalizedValue = normalizeDateTime(value);
                if (normalizedValue) {
                    // 尝试解析标准化的日期字符串
                    const [datePart, timePart] = normalizedValue.split(' ');
                    if (datePart && timePart) {
                        const [year, month, day] = datePart.split('-').map(Number);
                        const [hour, minute] = timePart.split(':').map(Number);
                        return parseZonedDateTime(`${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}[${getLocalTimeZone()}]`);
                    }
                }
            } catch (error) {
                console.warn('Failed to parse initial date value:', error);
            }
        }
        // 默认为空
        return null;
    });

    // 监听value变化，更新内部state
    React.useEffect(() => {
        if (value) {
            try {
                const normalizedValue = normalizeDateTime(value);
                if (normalizedValue) {
                    const [datePart, timePart] = normalizedValue.split(' ');
                    if (datePart && timePart) {
                        const [year, month, day] = datePart.split('-').map(Number);
                        const [hour, minute] = timePart.split(':').map(Number);
                        const newDate = parseZonedDateTime(`${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}[${getLocalTimeZone()}]`);
                        setDate(newDate);
                    }
                }
            } catch (error) {
                console.warn('Failed to parse updated date value:', error);
            }
        } else {
            setDate(null);
        }
    }, [value]);

    // DateInput onChange - 保持原有逻辑
    const handleDateInputChange = (newDate: ZonedDateTime | null) => {
        setDate(newDate);
        onSelect(formatDate(newDate) || "");
    };

    // DatePickerView onChange - 重构后的逻辑
    const handleDatePickerChange = (value: Date) => {
        if (value) {
            try {
                // 直接使用传入的Date对象创建ZonedDateTime，避免时区转换问题
                const zoned = parseZonedDateTime(
                    `${value.getFullYear()}-${(value.getMonth() + 1).toString().padStart(2, '0')}-${value.getDate().toString().padStart(2, '0')}T${value.getHours().toString().padStart(2, '0')}:${value.getMinutes().toString().padStart(2, '0')}[${getLocalTimeZone()}]`
                );
                setDate(zoned);
                onSelect(formatDate(zoned) || "");
            } catch (error) {
                console.error('Failed to parse date picker value:', error);
            }
        }
    };

    const labelRenderer = (type: string, data: number) => {
        switch (type) {
          case 'year':
            return data + '年'
          case 'month':
            return data + '月'
          case 'day':
            return data + '日'
          case 'hour':
            return data + '时'
          case 'minute':
            return data + '分'
          default:
            return data
        }
    }

    // DatePickerView 需要 JS Date 类型 - 优化转换逻辑
    const datePickerValue = React.useMemo(() => {
        if (!date) return new Date(); // 提供默认值避免undefined
        try {
            // 直接使用ZonedDateTime的本地时间，避免时区偏移
            return new Date(
                date.year,
                date.month - 1,
                date.day,
                date.hour,
                date.minute,
                0, // 明确设置秒为0
                0  // 明确设置毫秒为0
            );
        } catch (error) {
            console.error('Failed to convert ZonedDateTime to Date:', error);
            return new Date();
        }
    }, [date]);

    const handleClose = () => setIsDrawerOpen(false);

    const formatDate = (date: ZonedDateTime | null) => {
      if (!date) return '';
      const pad = (n: number) => n.toString().padStart(2, '0');
      return `${date.year}-${pad(date.month)}-${pad(date.day)} ${pad(date.hour)}:${pad(date.minute)}`;
    };

    return (
        <div className="flex items-center w-full justify-center font-sans ">
            <div className="w-full space-y-4 rounded-lg ">
                <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen} onClose={handleClose} aria-describedby="birthtime" modal={true} handleOnly={true}>
                    <DrawerTrigger asChild>
                        <Button variant="outline" className="w-full justify-start shadow-none font-normal text-base bg-white" value="选择出生时间">
                            {formatDate(date) || "选择出生时间"}
                        </Button>
                    </DrawerTrigger>
                    <DrawerContent>
                        <DrawerHeader className='px-0 pb-1'>
                            <DrawerTitle className="flex w-full">
                                <I18nProvider locale="zh-CN">
                                <DateInput
                                    aria-describedby="birthtime"
                                    granularity="minute"
                                    value={date}
                                    onChange={handleDateInputChange}
                                    aria-label="选择出生日期"
                                    hideTimeZone={true}
                                    classNames={{
                                        input: 'justify-center text-base',
                                    }}
                                />
                                </I18nProvider>
                            </DrawerTitle>
                        </DrawerHeader>
                        <DatePickerView
                            value={datePickerValue}
                            defaultValue={new Date()}
                            precision="minute"
                            mouseWheel={true}
                            onChange={handleDatePickerChange}
                            renderLabel={labelRenderer}
                            min={new Date(1000, 0, 1)}
                            max={new Date(2035, 11, 31)}
                        />
                    </DrawerContent>
                </Drawer>
            </div>
        </div>
    );
}

                                                               
                                                               
                                                               
                                                               
                                                               
                                                               
                                                               
                                                               