"use client"

import React, { memo } from 'react'
import { cn } from '@/lib/utils'

interface OptimizedGridProps {
  children: React.ReactNode
  className?: string
  cols?: 2 | 3 | 4
  gap?: 'sm' | 'md' | 'lg'
}

/**
 * 优化的网格组件
 * 专门为移动端大量元素渲染优化
 */
const OptimizedGrid = memo<OptimizedGridProps>(({ 
  children, 
  className, 
  cols = 3,
  gap = 'md'
}) => {
  const gridCols = {
    2: 'grid-cols-2',
    3: 'grid-cols-3', 
    4: 'grid-cols-4'
  }

  const gridGap = {
    sm: 'gap-2',
    md: 'gap-3 sm:gap-4',
    lg: 'gap-4 sm:gap-6'
  }

  return (
    <div
      className={cn(
        // 基础网格布局
        'grid',
        gridCols[cols],
        gridGap[gap],
        // 性能优化
        'will-change-contents transform-gpu',
        // 自定义类名
        className
      )}
      style={{
        // 强制硬件加速
        transform: 'translateZ(0)',
        // 优化重绘
        contain: 'layout style paint'
      }}
    >
      {children}
    </div>
  )
})

OptimizedGrid.displayName = 'OptimizedGrid'

export default OptimizedGrid
