"use client"

import React, { memo, forwardRef } from 'react'
import { cn } from '@/lib/utils'

interface OptimizedScrollContainerProps {
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

/**
 * 优化的滚动容器组件
 * 专门为移动端滚动性能优化设计
 */
const OptimizedScrollContainer = memo(forwardRef<HTMLDivElement, OptimizedScrollContainerProps>(
  ({ children, className, style }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          // 基础滚动样式
          "overflow-y-auto overflow-x-hidden",
          // 移动端滚动优化
          "scroll-container",
          // 硬件加速
          "will-change-scroll transform-gpu",
          // 自定义类名
          className
        )}
        style={{
          // 强制硬件加速
          transform: 'translateZ(0)',
          // WebKit滚动优化
          WebkitOverflowScrolling: 'touch',
          // 防止过度滚动
          overscrollBehavior: 'contain',
          // 自定义样式
          ...style
        }}
      >
        {children}
      </div>
    )
  }
))

OptimizedScrollContainer.displayName = 'OptimizedScrollContainer'

export default OptimizedScrollContainer
