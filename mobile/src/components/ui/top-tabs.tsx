import { cn } from "@/lib/utils"

export const Tabs_1 = ({ tabs, activeTab, onTabClick }: { tabs: any[], activeTab: string | null, onTabClick: (tab: any) => void }) => {
    return (
        <div className="flex w-full mobile-top-nav justify-around bg-background backdrop-blur-sm supports-backdrop-filter:bg-background/60">
            {tabs.map((tab) => (
                <button
                    key={tab.id}
                    onClick={() => onTabClick(tab.id as any)}
                    className={`flex-1 py-2 px-2 text-sm font-medium font-serif transition-colors border-b-2 ${activeTab === tab.id
                        ? "text-primary border-primary bg-muted/30"
                        : "text-gray-600 border-transparent hover:text-primary"
                        }`}
                >
                    {tab.label}
                </button>
            ))}
        </div>
    )
}

export const Tabs_2 = ({ tabs, activeTab, onTabClick }: { tabs: any[], activeTab: string | null, onTabClick: (tab: any) => void }) => {
    return (
        <div className="flex mobile-top-nav sticky top-0 z-10 bg-background border-b m-2 border-border/40 backdrop-blur-sm supports-backdrop-filter:bg-background/60" >
            <div className="flex items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground grid w-full grid-cols-3 h-10">
                {tabs.map((tab) => (
                    <button
                        key={tab.id}
                        onClick={() => onTabClick(tab.id as any)}
                        className={`inline-flex items-center justify-center whitespace-nowrap rounded-md py-1 px-1 font-medium ring-offset-background transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 
                            ${activeTab === tab.id ? "bg-background text-foreground shadow-sm text-sm" : "text-foreground"}`}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>
        </div>
    )
}

export const Tabs_3 = ({ tabs, activeTab, onTabClick }: { tabs: any[], activeTab: string | null, onTabClick: (tab: any) => void }) => {
    return (
        <div className="flex w-full mobile-top-nav justify-around bg-background backdrop-blur-sm">
            {tabs.map((tab) => (
                <button
                    key={tab.id}
                    onClick={() => onTabClick(tab.id as any)}
                    className="relative flex-1 py-4 px-2 text-sm transition-colors">
                    <div className={`relative mx-6 flex items-center justify-center gap-1 ${activeTab === tab.id ? 'text-primary' : 'text-foreground'}`}>
                        {tab.label}
                        <span
                            className={cn("pointer-events-none absolute left-0 -bottom-1 h-[2px] w-0 bg-primary transition-all group-hover/link:left-0 group-hover/link:w-full group-hover/link:translate-x-0",
                                activeTab === tab.id ? 'w-full left-0 translate-x-0 '
                                    : 'group-hover/navlink:left-0 group-hover/navlink:w-full group-hover/navlink:translate-x-0'
                            )}
                        />
                    </div> 
                </button>
            ))}
        </div>
    )
}

export const SecionTab = ({ tabs, activeTab, onTabClick }: { tabs: any[], activeTab: string | null, onTabClick: (tab: any) => void }) => {
    return (
        <div className="sticky top-0 z-10 bg-background backdrop-blur-md mb-4 will-change-transform" style={{ transform: 'translateZ(0)' }}>
            <div className="flex items-center justify-start gap-2 p-1 text-muted-foreground h-10">
                {tabs.map((tab) => (
                    <button
                        key={tab.id}
                        onClick={() => onTabClick(tab.id as any)}
                        className={`inline-flex items-center justify-center whitespace-nowrap rounded-xl py-1 px-4 ring-offset-background transition-colors duration-150 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 will-change-transform
                            ${activeTab === tab.id ? "text-primary-foreground shadow-sm text-sm bg-primary" : "bg-background border-primary border text-foreground"}`}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>
        </div>
    )
}