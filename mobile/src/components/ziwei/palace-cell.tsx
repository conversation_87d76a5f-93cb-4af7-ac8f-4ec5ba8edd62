'use client'
import { getFortunePalaceName, isPalaceTriangularPositions } from '@/lib/astro-utils'
import { FortuneData } from '@/types/divinate'

interface PalaceCellProps {
    palace: any
    position: string
    palace_index: number
    showFortuneData: FortuneData | null
    selectedPalace?: number | null
    onPalaceClick?: (index: number) => void
}

export const PalaceCell: React.FC<PalaceCellProps> = ({ 
    palace, 
    position, 
    palace_index, 
    showFortuneData,
    selectedPalace = null,
    onPalaceClick
}) => {
        // 判断是否为命宫，设置背景色
        let bgColor = 'bg-white'
        let cur_palace_name = palace.name
        if (showFortuneData) {
            cur_palace_name = showFortuneData.fortuneData[showFortuneData.fortuneData.length - 1].palaceNames[palace_index]
        }
        
        // 根据选中状态设置背景色
        if (selectedPalace !== null) {
            if (selectedPalace === palace_index) {
                // 被点击的宫位
                bgColor = 'bg-blue-50'
            } else {
                // 计算三合位置（取模12确保在0-11范围内）
                if (isPalaceTriangularPositions(palace_index, selectedPalace)) {
                    bgColor = 'bg-muted/50'
                }
            }
        } else {
            // 默认背景色设置
            if (cur_palace_name === '命宫') {
                bgColor = 'bg-blue-50'
            } else if (cur_palace_name === '官禄' || cur_palace_name === '迁移' || cur_palace_name === '财帛') {
                bgColor = 'bg-muted/50'
            }
        }

        // 根据位置设置边框样式，避免重合边框变粗
        let borderClass = ''
        switch (position) {
            case '1-1':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '1-2':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '1-3':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '1-4':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '2-1':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '2-2':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '3-1':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '3-2':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '4-1':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '4-2':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '4-3':
                borderClass = 'border-b border-r border-gray-400'
                break
            case '4-4':
                borderClass = 'border-b border-r border-gray-400'
                break
            default:
                borderClass = 'border-0 border-gray-400'
        }

        const handleClick = () => {
            if (onPalaceClick) {
                onPalaceClick(palace_index)
            }
        }

        return (
            <div 
                className={`flex flex-col justify-between px-[1px] py-0.5 ${borderClass} md:text-base text-xs ${bgColor} cursor-pointer hover:bg-opacity-80 transition-colors`}
                onClick={handleClick}
            >
                {/* 星曜 */}
                <div className="mb-1 flex flex-row-reverse leading-none flex-1 md:text-sm text-[10px]">
                    {/* 主星 */}
                    {palace.majorStars.map((item: any, index: number) => (
                        <div key={index} className="flex flex-col items-center leading-none md:w-4">
                            <span className="text-red-600 writing-mode-vertical-rl mb-0.5" >
                                {item.name}
                            </span>
                            {/* 亮度 */}
                            <span className="writing-mode-vertical-rl mb-0.5 text-[10px] md:text-[11px]">
                                {item.brightness || '\u00A0'} 
                            </span>
                            {/* 四化 */}
                            {(!showFortuneData || showFortuneData.mutagens.length < 3) && <span className="writing-mode-vertical-rl text-red-500 inline-block md:text-xs">
                                {item?.mutagen || '\u00A0'}
                            </span>}
                            {showFortuneData && showFortuneData.mutagens.length > 0 && 
                                showFortuneData.mutagens.map((mutagen: Record<string, string>, index: number) => (
                                    <span key={index} className="writing-mode-vertical-rl md:text-xs text-[10px]" 
                                    style={{
                                        color: showFortuneData.fortuneColor[showFortuneData.fortuneColor.length > 3 ? showFortuneData.fortuneColor.length-3+index: index],
                                        visibility: mutagen[item.name] ? 'visible' : 'hidden'
                                    }}>
                                        {mutagen[item.name] || '　'}
                                    </span>
                                ))
                            }
                        </div>
                    ))}
                    {/* 辅星和杂曜 */}
                    {palace.minorStars.slice(0, 8 - palace.majorStars.length).map((item: any, index: number) => (
                        <div key={index} className="flex flex-col items-center leading-none md:w-4">
                            <span
                                key={index}
                                className={`writing-mode-vertical-rl mb-0.5 ${item.type === 'tough' ? 'text-primary' : 'text-purple-600'}`}
                            >
                                {item.name}
                            </span>
                            <span className="writing-mode-vertical-rl mb-0.5 text-[10px] md:text-[11px]">
                                {item.brightness || '　'} 
                            </span>
                            {(!showFortuneData || showFortuneData.mutagens.length < 3) && <span className="writing-mode-vertical-rl text-red-500 inline-block md:text-xs">
                                {item?.mutagen || '　'}
                            </span>}
                            {showFortuneData && showFortuneData.mutagens.length > 0 && 
                                showFortuneData.mutagens.map((mutagen: Record<string, string>, index: number) => (
                                    <span key={index} className="writing-mode-vertical-rl md:text-xs" 
                                        style={{color: showFortuneData.fortuneColor[showFortuneData.fortuneColor.length > 3 ? showFortuneData.fortuneColor.length-3+index: index],
                                            visibility: mutagen[item.name] ? 'visible' : 'hidden'
                                        }}>
                                        {mutagen[item.name] || '　'}
                                    </span>
                                ))
                            }
                        </div>
                    ))}
                    {palace.adjectiveStars.slice(0, 8 - palace.majorStars.length - palace.minorStars.length).map((item: any, index: number) => (
                        <div key={index} className="flex flex-col items-center md:w-4">
                            <span className="writing-mode-vertical-rl mb-0.5" >
                                {item.name}
                            </span>
                            {item.brightness && <span className="writing-mode-vertical-rl">
                                {item.brightness || '\u00A0'} 
                            </span>}
                        </div>
                    ))}
                </div>

                {/* 小限 */}
                {palace.ages.length > 0 && !showFortuneData && (
                    <div className="md:text-xs text-[8px] text-gray-600 mb-0.5 text-center">
                        {palace.ages.slice(0, 7).join(',')}
                    </div>
                )}
                 {/* 运限星曜 */}
                 {showFortuneData && showFortuneData.fortuneData.length > 0 && (
                     <div className="md:text-[11px] text-[9px] text-gray-600 mb-0.5 text-center flex justify-start">
                         {showFortuneData.fortuneData.map((item: any, index: number) => 
                             item.stars[palace_index].length > 0 && (
                                 <div key={index} className="flex items-center leading-none">
                                     {item.stars[palace_index].map((star: any, starIndex: number) => (
                                         <span key={starIndex} className="writing-mode-vertical-rl mb-0.5">
                                             {star.name}
                                         </span>
                                     ))} 
                                 </div>
                             )
                         )}
                     </div>
                 )}
                <div className="flex flex-1 items-end justify-between">
                    {/* 神煞 */}
                    <div className="flex items-center md:text-xs text-[9px]">
                        { (!showFortuneData || showFortuneData.type === '大限') ? (<div className="flex flex-col gap-0 text-gray-600 text-end justify-end items-end">
                            <span className="block leading-none"> {palace.boshi12} </span>
                            <span className="block leading-none"> {palace.jiangqian12} </span>
                            <span className="block leading-none"> {palace.suiqian12} </span>
                        </div>) : (<div className="flex flex-col text-gray-600 text-end justify-end items-end ">
                            <span className="block leading-none"> {palace.boshi12} </span>
                            <span className="block leading-none"> {showFortuneData.yearlyDecStars[palace_index][0]} </span>
                            <span className="block leading-none"> {showFortuneData.yearlyDecStars[palace_index][1]} </span>
                        </div>)}
                    </div>
                    <div className="text-center  ">
                        {/* 大限 */}
                        {palace.decadal.range.length > 0 && !showFortuneData && (
                            <div className="md:text-xs text-[10px] text-gray-600 mb-1 text-center">
                                {palace.decadal.range.join('-')}
                            </div>
                        )}
                        {!showFortuneData && (
                        <div className="flex items-center text-[10px] md:text-xs justify-center ">
                            {palace.isBodyPalace && (
                                <div className=" text-gray-600 justify-center items-center">
                                    <span className="text-secondary leading-none">身宫</span>
                                </div>
                            )}
                            {palace.isOriginalPalace && (
                                <div className=" text-gray-600 justify-center items-center">
                                    <span className="text-secondary leading-none">来因宫</span>
                                </div>
                            )}
                            {!palace.isOriginalPalace && !palace.isBodyPalace && (
                                <div className=" text-gray-600 justify-center items-center">
                                    <span className="text-secondary leading-none">{'\u00A0'}</span>
                                </div>
                            )}
                        </div>
                        )}
                        <div className="flex flex-col justify-end md:text-sm text-[10px]">
                        {/* 宫名 - 统一显示palace.name和运限宫名 */}
                        {showFortuneData ? (
                            <div className="flex justify-center items-end flex-row-reverse">
                                {/* 将palace.name和运限宫名合并排列，每列最多3个 */}
                                {(() => {
                                    // 原宫位名称作为第一个元素
                                    const originalPalaceName = palace.name === '仆役' ? '交友' : palace.name;
                                    // 运限宫名
                                    const fortuneNames = showFortuneData.palaceNames[palace_index].map((item, index) => 
                                        getFortunePalaceName(item, index)
                                    );
                                    // 合并所有宫名，原宫名在前
                                    const allPalaceNames = [originalPalaceName, ...fortuneNames];
                                    // 颜色数组，原宫名用红色，其他用运限颜色
                                    const allColors = ['#dc2626', ...showFortuneData.fortuneColor]; // #dc2626 是 text-red-600
                                    
                                    const columns = [];
                                    
                                    // 每列最多3个宫名
                                    for (let i = 0; i < allPalaceNames.length; i += 3) {
                                        const columnNames = allPalaceNames.slice(i, i + 3);
                                        const columnColors = allColors.slice(i, i + 3);
                                        
                                        columns.push(
                                            <div key={i} className="flex flex-col-reverse items-center gap-0.5">
                                                {columnNames.map((item, index) => (
                                                    <div 
                                                        key={i + index} 
                                                        className="flex leading-none text-center md:px-0.5"
                                                        style={{ color: columnColors[index] }}
                                                    >
                                                        {item}
                                                    </div>
                                                ))}
                                            </div>
                                        );
                                    }
                                    
                                    return columns;
                                })()}
                            </div>
                        ) : (
                            /* 没有运限数据时，只显示原宫位名称 */
                            <div className="text-red-600 flex-1 md:text-base text-xs text-center">
                                {palace.name === '仆役' ? '交友' : palace.name}
                            </div>
                        )}
                        </div>
                    </div>
                    <div className="flex flex-col justify-end items-end ">
                        {/* 十二长生 */}
                        {palace.changsheng12 && (
                            <div className="md:text-xs text-[9px] text-center">{palace.changsheng12}</div>
                        )}
                        {/* 干支 */}
                        <div className="flex justify-end items-end leading-none">
                            <span className="md:text-xs text-[10px] writing-mode-vertical-rl">{palace.heavenlyStem}{palace.earthlyBranch}</span>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
