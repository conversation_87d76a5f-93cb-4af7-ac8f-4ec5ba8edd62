import React, { useState, useMemo } from 'react'
import { Card } from '@/components/ui/card'
import { getTianGanWuXingYinYang, getMutagenColor, getMutagenMap } from '@/lib/astro-utils'
import { HoroscopeItem } from 'iztro/lib/data/types'
import { ZiweiFortuneRequest, FortuneColorMap, FortuneData } from '@/types/divinate'
import { PalaceCell } from './palace-cell'

interface ZiweiChartProps {
    name: string
    gender: string
    birthTime: string
    trueSolarTime: string
    // 添加 iztro 原始数据支持
    astroData?: any
    // 运限请求数据，可选
    fortuneRequest?: ZiweiFortuneRequest | null
}

const ZiweiChart: React.FC<ZiweiChartProps> = ({
    name,
    gender,
    birthTime,
    trueSolarTime,
    astroData,
    fortuneRequest
}) => {
    //console.log('ziwei-chart start')
    const [selectedPalace, setSelectedPalace] = useState<number | null>(null)
    
    const tianyan_yinyang = getTianGanWuXingYinYang(astroData.chineseDate[0])
    const gender_yinYang = tianyan_yinyang?.yinyang + (gender === 'male' ? '男' : '女')

    // 检查是否有运限请求数据
    const hasFortuneRequest = fortuneRequest !== null && fortuneRequest !== undefined

    // 使用 useMemo 缓存运限数据计算结果，避免每次渲染都重新计算
    const fortuneData = useMemo((): FortuneData | null => {
        if (!hasFortuneRequest || !fortuneRequest) return null

        try {
            const dateStr = fortuneRequest.date.toLocaleDateString('zh-CN')
            const timeIndex = fortuneRequest.timeIndex !== undefined ? fortuneRequest.timeIndex : 0

            const fortuneData = astroData.horoscope(new Date(dateStr), timeIndex)
            const selectFortuneData: HoroscopeItem[] = []
            const fortuneColor: string[] = []
            const palaceNames: string[][] = []
            const mutagens: Record<string, string>[] = []
            
            //最多只取三个运限盘
            if (fortuneRequest.type === '大限') {
                selectFortuneData.push(fortuneData.decadal)
                fortuneColor.push(FortuneColorMap[fortuneRequest.type])
                fortuneData.decadal.palaceNames.forEach((name: string) => {
                    palaceNames.push([name])
                })
                mutagens.push(getMutagenMap(fortuneData.decadal.mutagen))

            } else if (fortuneRequest.type === '流年') {
                selectFortuneData.push(fortuneData.decadal)
                selectFortuneData.push(fortuneData.yearly)
                fortuneColor.push(FortuneColorMap['大限'])
                fortuneColor.push(FortuneColorMap[fortuneRequest.type])
                fortuneData.decadal.palaceNames.forEach((name: string, index: number) => {
                    palaceNames.push([fortuneData.decadal.palaceNames[index], fortuneData.yearly.palaceNames[index]])
                })
                mutagens.push(getMutagenMap(fortuneData.decadal.mutagen))
                mutagens.push(getMutagenMap(fortuneData.yearly.mutagen))
            } else if (fortuneRequest.type === '流月') {
                selectFortuneData.push(fortuneData.decadal)
                selectFortuneData.push(fortuneData.yearly)
                selectFortuneData.push(fortuneData.monthly)
                fortuneColor.push(FortuneColorMap['大限'])
                fortuneColor.push(FortuneColorMap['流年'])
                fortuneColor.push(FortuneColorMap[fortuneRequest.type])
                fortuneData.decadal.palaceNames.forEach((name: string, index: number) => {
                    palaceNames.push([fortuneData.decadal.palaceNames[index], 
                                     fortuneData.yearly.palaceNames[index], 
                                     fortuneData.monthly.palaceNames[index]])
                })
                mutagens.push(getMutagenMap(fortuneData.decadal.mutagen))
                mutagens.push(getMutagenMap(fortuneData.yearly.mutagen))
                mutagens.push(getMutagenMap(fortuneData.monthly.mutagen))
            } else if (fortuneRequest.type === '流日') {
                selectFortuneData.push(fortuneData.yearly)
                selectFortuneData.push(fortuneData.monthly)
                selectFortuneData.push(fortuneData.daily)
                fortuneColor.push(FortuneColorMap['大限'])
                fortuneColor.push(FortuneColorMap['流年'])
                fortuneColor.push(FortuneColorMap['流月'])
                fortuneColor.push(FortuneColorMap[fortuneRequest.type])
                fortuneData.decadal.palaceNames.forEach((name:string, index: number) => {
                    palaceNames.push([fortuneData.decadal.palaceNames[index], 
                                     fortuneData.yearly.palaceNames[index], 
                                     fortuneData.monthly.palaceNames[index],
                                     fortuneData.daily.palaceNames[index]])
                })
                mutagens.push(getMutagenMap(fortuneData.yearly.mutagen))
                mutagens.push(getMutagenMap(fortuneData.monthly.mutagen))
                mutagens.push(getMutagenMap(fortuneData.daily.mutagen))
            } else if (fortuneRequest.type === '流时') {
                selectFortuneData.push(fortuneData.monthly)
                selectFortuneData.push(fortuneData.daily)
                selectFortuneData.push(fortuneData.hourly)
                fortuneColor.push(FortuneColorMap['大限'])
                fortuneColor.push(FortuneColorMap['流年'])
                fortuneColor.push(FortuneColorMap['流月'])
                fortuneColor.push(FortuneColorMap['流日'])
                fortuneColor.push(FortuneColorMap[fortuneRequest.type])
                fortuneData.decadal.palaceNames.forEach((name: string, index: number) => {
                    palaceNames.push([fortuneData.decadal.palaceNames[index], 
                                     fortuneData.yearly.palaceNames[index], 
                                     fortuneData.monthly.palaceNames[index],
                                     fortuneData.daily.palaceNames[index],
                                     fortuneData.hourly.palaceNames[index]])
                })
                mutagens.push(getMutagenMap(fortuneData.monthly.mutagen))
                mutagens.push(getMutagenMap(fortuneData.daily.mutagen))
                mutagens.push(getMutagenMap(fortuneData.hourly.mutagen))
            }

            const yearlyDecStars: string[][] = []
            if (fortuneRequest.type !== '大限') {
                fortuneData.yearly.yearlyDecStar.jiangqian12.map((item: string, index: number) => {
                    yearlyDecStars.push([item, fortuneData.yearly.yearlyDecStar.suiqian12[index]])
                })
            }
            return {
                type: fortuneRequest.type,
                date: dateStr,
                time: timeIndex,
                fortuneData: selectFortuneData,
                fortuneColor: fortuneColor,
                palaceNames: palaceNames,
                mutagens: mutagens,
                yearlyDecStars: yearlyDecStars
            }
        } catch (error) {
            console.error('运限数据计算失败:', error)
            return null
        }
    }, [hasFortuneRequest, fortuneRequest, astroData]) // 依赖项：只有这些值变化时才重新计算

    const handlePalaceCellClick = (index: number) => {
        // 如果点击的是已选中的宫位，则取消选中
        if (selectedPalace === index) {
            setSelectedPalace(null)
        } else {
            // 否则选中该宫位
            setSelectedPalace(index)
        }
    }

    //console.log('fortuneData', fortuneData)

    // 宫位组件
    // 中央信息区域
    const CenterInfo: React.FC = () => (
        <div className="col-span-2 row-span-2 text-base sm:text-sm py-0.5 px-1 md:px-6 md:py-2 bg-white overflow-hidden border-gray-400 border-r border-b">
            {/* 性别和五行 */}
            <div className="text-center md:text-xl sm:text-lg font-bold mt-6 mb-4">{gender_yinYang} {astroData.fiveElementsClass}</div>
            <div className="flex justify-center">
            <div className="flex flex-col gap-2 items-start text-[10px] md:text-base min-w-[148px]">
                <div className="flex gap-1 md:gap-4 ">
                    <span>出生时间</span>
                    <span>{birthTime}</span>
                </div>
                <div className="flex gap-2 md:gap-4 ">
                    <span>真太阳时 </span>
                    <span>{trueSolarTime}</span>
                </div>
                <div className="flex gap-2 md:gap-4">
                    <span>农历 </span>
                    <span>{astroData.lunarDate} {astroData.time}</span>
                </div>


                {/* 四柱 */}
                <div className="flex justify-center gap-4">
                    <div className="text-center mb-0.5">四柱</div>
                    <div>
                        <div className="text-center text-primary font-bold">{astroData.chineseDate}</div>
                    </div>
                </div>

                {/* 命主身主 */}
                <div className="flex w-full">
                    <div className="text-red-600 w-1/2">命主 {astroData.soul}</div>
                    <div className="text-red-600 ">身主 {astroData.body}</div>
                </div>

                {/* 斗君 暂缺 */}

                {/* 四化颜色 */}
                <div className="flex flex-1 justify-between gap-1 ">
                    <div className="mb-0.5">运限</div>
                    <div className="flex justify-center">
                        <span style={{color: FortuneColorMap['大限']}}>大限</span>|
                        <span style={{color: FortuneColorMap['流年']}}>流年</span>|
                        <span style={{color: FortuneColorMap['流月']}}>流月</span>|
                        <span style={{color: FortuneColorMap['流日']}}>流日</span>|
                        <span style={{color: FortuneColorMap['流时']}}>流时</span>
                    </div>
                </div>
            </div>
            </div>
        </div>
    )

    return (
        <div className="flex min-w-full justify-center overflow-x-auto px-1 py-1 md:max-w-4xl md:mx-auto md:rounded-2xl rounded-none border-none shadow-none bg-white">
            {/* 添加外层边框容器 */}
            <div className="flex w-full xs:w-full justify-center border-t border-l border-gray-400 rounded-none">
                <div className="grid grid-cols-4 xs:grid-cols-4 grid-rows-4 bg-white gap-0 w-full min-h-[530px] md:min-h-[660px] text-[8px] sm:text-xs">
                {/* <div className="inline-grid grid-cols-[repeat(4,max-content)] xs:grid-cols-4 grid-rows-4 bg-white gap-0 w-full min-h-[530px] md:min-h-[660px] text-[8px] sm:text-xs"> */}
                    {/* 第一行 */}
                    <PalaceCell palace={astroData.palaces[3]} position="1-1" palace_index={3} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (巳) */}
                    <PalaceCell palace={astroData.palaces[4]} position="1-2" palace_index={4} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (午) */}
                    <PalaceCell palace={astroData.palaces[5]} position="1-3" palace_index={5} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (未) */}
                    <PalaceCell palace={astroData.palaces[6]} position="1-4" palace_index={6} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (申) */}

                    {/* 第二行 */}
                    <PalaceCell palace={astroData.palaces[2]} position="2-1" palace_index={2} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (辰) */}
                    <CenterInfo /> {/* 中央信息区域，占2x2 */}
                    <PalaceCell palace={astroData.palaces[7]} position="2-2" palace_index={7} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (酉) */}

                    {/* 第三行 */}
                    <PalaceCell palace={astroData.palaces[1]} position="3-1" palace_index={1} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (卯) */}
                    {/* 中央信息区域继续 */}
                    <PalaceCell palace={astroData.palaces[8]} position="3-2" palace_index={8} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (戌) */}

                    {/* 第四行 */}
                    <PalaceCell palace={astroData.palaces[0]} position="4-1" palace_index={0} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (寅) */}
                    <PalaceCell palace={astroData.palaces[11]} position="4-2" palace_index={11} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (丑) */}
                    <PalaceCell palace={astroData.palaces[10]} position="4-3" palace_index={10} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (子) */}
                    <PalaceCell palace={astroData.palaces[9]} position="4-4" palace_index={9} showFortuneData={fortuneData} selectedPalace={selectedPalace} onPalaceClick={handlePalaceCellClick}/> {/* (亥) */}
                </div>
            </div>
        </div>
    )
}

export default ZiweiChart 