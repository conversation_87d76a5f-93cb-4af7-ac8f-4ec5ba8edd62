'use client'
import { ZiweiFortuneRequest } from '@/types/divinate'
import { FortuneTime, FortuneTimeRef } from "@/components/ziwei/fortune-time"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Ziwei<PERSON>hart  from "@/components/ziwei/ziwei-chart"
import { getZiweiResult } from "@/lib/divinate/ziwei"
import { Card, CardContent } from "@/components/ui/card"
import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useRef, useState } from 'react'
import Loading from '@/app/ziwei/divinate/result/loading'
import { ZiweiDivinateRequest } from '@/types/divinate'

// 定义组件的props接口
interface ZiweiResultContentProps {
  ziweiRequest: ZiweiDivinateRequest
}

export default function ZiweiResultContent({ 
  ziweiRequest, 
}: ZiweiResultContentProps) {
  const [astroData, setAstroData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("本命") // 添加tab状态管理
  const [fortuneRequest, setFortuneRequest] = useState<ZiweiFortuneRequest | null>(null) // 运限请求状态
  const fortuneTimeRef = useRef<FortuneTimeRef>(null)
  const router = useRouter()

  // 加载数据并计算紫微斗数
  const loadDataAndCalculate = useCallback(async () => {
    try {

      // 分批进行紫微斗数计算，避免一次性计算过多导致卡死
      const result = await new Promise<any>((resolve) => {
        setTimeout(() => {
          try {
            const astroResult = getZiweiResult(ziweiRequest)
            resolve(astroResult)
          } catch (error) {
            throw error
          }
        }, 0)
      })
      setAstroData(result)
    } catch (err) {
      console.error('紫微斗数计算失败:', err)
      setError(err instanceof Error ? err.message : '计算失败，请稍后再试')
    } finally {
      setIsLoading(false)
    }
  }, [ziweiRequest])

  useEffect(() => {
    // 添加性能监控
    loadDataAndCalculate().finally(() => {
    })
  }, [loadDataAndCalculate])

  // 返回测算页面
  const handleBackToCalculate = useCallback(() => {
    router.push('/ziwei/divinate')
  }, [router])

  // 切换到运限tab的处理函数
  const handleSwitchToFortuneTab = useCallback((tab: string) => {
    setActiveTab(tab)
  }, [])

  // 处理运限请求变化
  const handleFortuneRequestChange = useCallback((request: ZiweiFortuneRequest) => {
    setFortuneRequest(request)
  }, [])

  // 创建默认运限请求的辅助函数
  const createDefaultFortuneRequest = useCallback((type: string): ZiweiFortuneRequest => {
    return {
      type: type as '大限' | '流年' | '流月' | '流日' | '流时',
      date: new Date(),
      timeIndex: 6    //默认为午时
    }
  }, [])

  // 设置默认运限请求并更新FortuneTime组件
  const setDefaultFortuneRequest = useCallback((type: string) => {
    const defaultRequest = createDefaultFortuneRequest(type)
    setFortuneRequest(defaultRequest)
    // 设置FortuneTime组件的默认值
    fortuneTimeRef.current?.setDefaultValues(defaultRequest)
  }, [createDefaultFortuneRequest])

  // 处理tab变化 - 自动更新fortuneRequest.type
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value)
    // 当切换到运限tab时，更新或创建fortuneRequest
    if (value !== "本命") {
      if (fortuneRequest) {
        // 如果已有fortuneRequest，更新type
        setFortuneRequest({
          ...fortuneRequest,
          type: value as '大限' | '流年' | '流月' | '流日' | '流时'
        })
      } else {
        // 如果没有fortuneRequest，从FortuneTime组件获取当前选择状态来创建。目前的逻辑，正常不会走到这里
        const currentSelection = fortuneTimeRef.current?.getCurrentSelection()
        if (currentSelection) {
          try {
            const newFortuneRequest = currentSelection.createFortuneRequest(value as '大限' | '流年' | '流月' | '流日' | '流时', 
                                                                            currentSelection.selectedYear,
                                                                            currentSelection.selectedMonth,
                                                                            currentSelection.selectedDate?.getDate(),
                                                                            currentSelection.selectedTimeIndex)
            if (newFortuneRequest) {
              setFortuneRequest(newFortuneRequest)
            } else {
              // 如果createFortuneRequest返回null，说明没有有效选择，使用默认值
              setDefaultFortuneRequest(value)
            }
          } catch (error) {
            // 如果创建失败，使用默认值
            setDefaultFortuneRequest(value)
          }
        } else {
          // 如果无法获取当前选择，使用默认值
          setDefaultFortuneRequest(value)
        }
      }
    }
  }, [fortuneRequest, setDefaultFortuneRequest])

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Loading />
      </div>
    )
  }

  if (error || !ziweiRequest || !astroData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="shadow-lg mb-8">
          <CardContent>
            <div className="flex flex-col items-center justify-center h-[400px] text-center">
              <p className="text-red-500 mb-4">{error || '数据加载失败'}</p>
              <button
                onClick={handleBackToCalculate}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              >
                重新测算
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
        <div className="flex flex-col md:shadow-lg md:border-1 md:m-8 w-full md:max-w-2xl py-4 md:py-6 md:rounded-2xl">
          <div className="flex justify-center pb-0 gap-0">
            <h1 className="text-center text-xl md:text-2xl font-serif">紫微斗数星盘</h1>
          </div>
          <div className="flex flex-col flex-1 gap-2 px-0 md:px-6">
            <div className="flex flex-col flex-1 gap-2 md:gap-6">
              {/* 紫微斗数星盘 - 传入真实的 iztro 数据 */}
              <Tabs value={activeTab} onValueChange={handleTabChange} defaultValue="本命" className="flex flex-col flex-1">
                <TabsContent value="本命" className="flex justify-center flex-1">
                  <ZiweiChart
                    name={ziweiRequest.name}
                    gender={ziweiRequest.gender}
                    birthTime={ziweiRequest.solarTime || ''}
                    trueSolarTime={ziweiRequest.trueSolarTime}
                    astroData={astroData}
                  />
                </TabsContent>
                <TabsContent value="大限" className="flex justify-center flex-1">
                  <ZiweiChart
                    name={ziweiRequest.name}
                    gender={ziweiRequest.gender}
                    birthTime={ziweiRequest.solarTime || ''}
                    trueSolarTime={ziweiRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <TabsContent value="流年" className="flex justify-center flex-1">
                  <ZiweiChart
                    name={ziweiRequest.name}
                    gender={ziweiRequest.gender}
                    birthTime={ziweiRequest.solarTime || ''}
                    trueSolarTime={ziweiRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <TabsContent value="流月" className="flex justify-center flex-1">
                  <ZiweiChart
                    name={ziweiRequest.name}
                    gender={ziweiRequest.gender}
                    birthTime={ziweiRequest.solarTime || ''}
                    trueSolarTime={ziweiRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <TabsContent value="流日" className="flex justify-center flex-1">
                  <ZiweiChart
                    name={ziweiRequest.name}
                    gender={ziweiRequest.gender}
                    birthTime={ziweiRequest.solarTime || ''}
                    trueSolarTime={ziweiRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <TabsContent value="流时" className="flex justify-center flex-1">
                  <ZiweiChart
                    name={ziweiRequest.name}
                    gender={ziweiRequest.gender}
                    birthTime={ziweiRequest.solarTime || ''}
                    trueSolarTime={ziweiRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <div className="bg-white px-2 pd:mx-0">
                <TabsList className="bg-paper-secondary w-full justify-around" >
                  <TabsTrigger value="本命" className="w-1/6">本命盘</TabsTrigger>
                  <TabsTrigger value="大限" className="w-1/6">大限盘</TabsTrigger>
                  <TabsTrigger value="流年" className="w-1/6">流年盘</TabsTrigger>
                  <TabsTrigger value="流月" className="w-1/6">流月盘</TabsTrigger>
                  <TabsTrigger value="流日" className="w-1/6">流日盘</TabsTrigger>
                  <TabsTrigger value="流时" className="w-1/6">流时盘</TabsTrigger>
                </TabsList>
                </div>
              </Tabs>
            </div>
            <div className="flex justify-center">
              <FortuneTime 
                astroData={astroData} 
                onSelectChange={handleSwitchToFortuneTab}
                onFortuneRequestChange={handleFortuneRequestChange}
                ref={fortuneTimeRef}
              />
            </div>
          </div>
        </div>
  )
}