"use client"

import useS<PERSON> from 'swr'
import useSWRMutation from 'swr/mutation'
import { bookAPI, caseAPI, divinateAP<PERSON>, createFetcher } from '@/lib/api/api-client'
import { useAuth } from './use-auth'
import { <PERSON><PERSON>, BirthData } from '@/types/user'
import { CaseCreateRequest, CaseUpdateRequest } from '@/types/case'
import { CaseType, RelationType } from '@/types/enums'
import FunctionalAstrolabe from "iztro/lib/astro/FunctionalAstrolabe"

// 通用SWR配置
const defaultConfig = {
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  shouldRetryOnError: false,
  errorRetryCount: 2,
}

// 书籍相关hooks
export function useBookList(params: {
  page?: string
  size?: string
  is_completed?: string
  category?: string | null
} = {}) {
  const key = ['/book/list', params]
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => bookAPI.getBooks(params),
    defaultConfig
  )

  return {
    books: data?.items || [],
    total: data?.total || 0,
    totalPages: data?.total_pages || 0,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

export function useBookDetail(bookId: string | null) {
  // const { token } = useAuth()
  // const key = token ? ['/book', bookId] : null
  const key = bookId ? ['/book', bookId] : null
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    // () => booksAPI.getBookDetail(bookId, token || undefined),
    () => bookAPI.getBookDetail(bookId, undefined),
    defaultConfig
  )

  return {
    chapters: data?.chapter_list || [],
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

export function useChapterContent(chapterId: string | null) {
  //const { token } = useAuth()
  //const key = token && chapterId ? ['/book/chapter', chapterId] : null
  const key = chapterId ? ['/book/chapter', chapterId] : null

  const { data, error, isLoading, mutate } = useSWR(
    key,
    //() => booksAPI.getChapterContent(chapterId, token || undefined),
    () => bookAPI.getChapterContent(chapterId, undefined),
    defaultConfig
  )
  return {
    content: data?.chapters,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

// 案例相关hooks
// 案例列表
export function useCaseList(params: any = {}) {
  const { token } = useAuth()
  const key = token ? ['/cases/list', params] : null

  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => caseAPI.getCases(params, token || undefined),
    defaultConfig
  )

  return {
    cases: data?.items || [],
    total: data?.total || 0,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

// 案例详情
export function useCaseDetail(caseId: string | null) {
  const { token } = useAuth()
  const key = token && caseId ? ['/cases', caseId] : null

  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => caseAPI.getCaseDetail(caseId!, token || undefined),
    defaultConfig
  )

  return {
    case: data,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

// 创建案例的 mutation
export function useCreateCase() {
  const { token } = useAuth()

  // 从BirthData和astroData创建CaseCreateRequest的辅助函数
  const createCaseRequestFromData = (
    birthData: BirthData, 
    lunarTime: string,
    bazi: Bazi,
    caseType: CaseType
  ): CaseCreateRequest => {
    // 从astroData.chineseDate解析八字信息（空格分割的字符串）

    return {
      user_name: birthData.name,
      gender: birthData.gender,
      birth_time_solar: birthData.birthTime + ':00',  // 确保格式正确
      birth_time_lunar: lunarTime,
      bazi_year: bazi.year,
      bazi_month: bazi.month,
      bazi_day: bazi.day,
      bazi_time: bazi.time,
      birth_place: birthData.birthplace,
      useTrueSolarTime: birthData.useTrueSolarTime,
      isDST: birthData.isDST,
      useEarlyOrLateNight: birthData.useEarlyOrLateNight,
      longitude: birthData.longitude,
      divinate_result: null, // 改为null而不是空字符串
      relation_type: birthData.relationship as RelationType,
      tags: [],
      case_type: caseType
    }
  }

  return useSWRMutation(
    '/api/case/new',
    async (key: string, { arg }: { arg: { birthData: BirthData; lunarTime: string; bazi: Bazi; caseType: CaseType } }) => {
      try {
        const caseRequest = createCaseRequestFromData(arg.birthData, arg.lunarTime, arg.bazi, arg.caseType)
        const response = await caseAPI.createCase(caseRequest, token || undefined)
        return response || null
      } catch (error) {
        throw error
      }
    }
  )
}
// 删除案例
export function useDeleteCase() {
  const { token } = useAuth()

  return useSWRMutation(
    '/api/case/delete',
    async (key: string, { arg }: { arg: { caseId: string } }) => {
      try {
        const result = await caseAPI.deleteCase(arg.caseId, token || undefined)
        return result?.success || false
      } catch (error) {
        throw error
      }
    }
  )
}

// 更新案例的 mutation
export function useUpdateCase() {
  const { token } = useAuth()

  return useSWRMutation(
    '/api/case/update',
    async (key: string, { arg }: { arg: { caseId: string; updates: Partial<CaseUpdateRequest> } }) => {
      try {
        const result = await caseAPI.updateCase(arg.caseId, arg.updates, token || undefined)
        return result?.success || false
      } catch (error) {
        throw error
      }
    }
  )
}
// 紫微斗数测算获取基础分析结果
export function useZiweiDivinateAnalysis() {
  const { token } = useAuth()

  return useSWRMutation(
    '/api/ziwei/divinate/analysis',
    async (key: string, { arg }: { arg: { astroData: FunctionalAstrolabe } }) => {
      try {
        const result = await divinateAPI.divinateAnalysis(arg.astroData, token || undefined)
        return result || ''
      } catch (error) {
        throw error
      }
    }
  )
}


// 通用数据获取hook
export function useData<T>(
  endpoint: string | null,
  requireAuth: boolean = false
) {
  const { token } = useAuth()
  const fetcher = createFetcher(requireAuth ? token || undefined : undefined)
  
  // 如果需要认证但没有token，不发起请求
  const key = requireAuth && !token ? null : endpoint
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    fetcher,
    defaultConfig
  )

  return {
    data,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
} 