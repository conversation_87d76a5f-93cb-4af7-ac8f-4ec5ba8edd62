// hooks/use-auth.ts
"use client"

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { AuthService, AuthState, TokenManager } from '@/lib/auth'

// 全局认证状态管理
let globalAuthState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true
}

let listeners: Array<(state: AuthState) => void> = []

// 设置全局状态监听器，供认证服务调用
if (typeof window !== 'undefined') {
  (window as any).authStateListeners = listeners
}

// 更新全局认证状态
const updateAuthState = (newState: Partial<AuthState>) => {
  globalAuthState = { ...globalAuthState, ...newState }
  listeners.forEach(listener => listener(globalAuthState))
}

// 获取当前路径的辅助函数
function getCurrentPath(): string {
  if (typeof window !== 'undefined') {
    return window.location.pathname + window.location.search
  }
  return '/'
}

// 认证Hook
export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>(globalAuthState)
  const router = useRouter()

  // 订阅认证状态变化
  useEffect(() => {
    const listener = (state: AuthState) => setAuthState(state)
    listeners.push(listener)
    
    return () => {
      listeners = listeners.filter(l => l !== listener)
    }
  }, [])

  // 简化的初始化 - 只获取当前状态，不重复初始化
  useEffect(() => {
    const initState = () => {
      // 只获取当前认证状态，不执行初始化
      const state = AuthService.getAuthState()
      updateAuthState({ ...state, isLoading: false })
    }

    // 如果认证系统已经初始化，直接获取状态
    if (AuthService.getInitializationStatus()) {
      initState()
    } else {
      // 如果还未初始化，等待初始化完成后再获取状态
      // 注意：实际的初始化由 AuthInitializer 组件负责
      const checkInit = () => {
        if (AuthService.getInitializationStatus()) {
          initState()
        } else {
          // 如果还没初始化，100ms后再检查
          setTimeout(checkInit, 100)
        }
      }
      checkInit()
    }
  }, [])

  // 登录方法
  const login = useCallback(async (credentials: {
    username: string
    password: string
    login_type: string
  }) => {
    try {
      // 直接调用 AuthService.login，不触发全局状态更新
      const { user, token } = await AuthService.login(credentials)
      
      // 手动更新当前组件的状态
      setAuthState({
        user,
        token,
        isAuthenticated: true,
        isLoading: false
      })
      
      // 更新全局状态，但不触发监听器
      globalAuthState = {
        user,
        token,
        isAuthenticated: true,
        isLoading: false
      }
      
      return { success: true }
    } catch (error: any) {
      setAuthState({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false
      })
      throw error
    }
  }, [])

  // 注册方法
  const register = useCallback(async (data: {
    email?: string
    phone?: string
    password: string
  }) => {
    return AuthService.register(data)
  }, [])

  // 登出方法
  const logout = useCallback(async () => {
    updateAuthState({ isLoading: true })
    
    try {
      await AuthService.logout()
      updateAuthState({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false
      })
    } catch (error) {
      console.error('登出失败:', error)
      // 即使失败也要清理状态
      updateAuthState({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false
      })
      router.push('/login')
    }
  }, [router])

  // 手动刷新Token - 用于测试和特殊场景
  const refreshToken = useCallback(async () => {
    try {
      const newToken = await AuthService.refreshToken()
      if (newToken) {
      updateAuthState({
        user: TokenManager.getUser(),
        token: newToken,
        isAuthenticated: true,
        isLoading: false
      })
        return true
      } else {
      updateAuthState({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false
      })
        return false
      }
    } catch (error) {
      console.error('Token刷新失败:', error)
      updateAuthState({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false
      })
      return false
    }
  }, [])

  return {
    // 状态
    user: authState.user,
    token: authState.token,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    
    // 方法
    login,
    register,
    logout,
    refreshToken
  }
}

// 认证保护Hook - 用于需要认证的页面
export function useAuthGuard(redirectTo: string = '/login') {
  const { isAuthenticated, isLoading, token } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // 如果还在加载中，不做任何操作
    if (isLoading) return

    // 检查token是否过期
    if (token && TokenManager.isTokenExpired()) {
      TokenManager.clearAuth()
      updateAuthState({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false
      })
      
      // token过期时也要带上callbackUrl
      const currentPath = getCurrentPath()
      const callbackUrl = encodeURIComponent(currentPath)
      if (typeof window !== 'undefined') {
        router.replace(`${redirectTo}?callbackUrl=${callbackUrl}`)
      }
      return
    }

    // 如果未认证且不在登录页，重定向到登录页
    if (!isAuthenticated && typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
      const currentPath = getCurrentPath()
      const callbackUrl = encodeURIComponent(currentPath)
      router.replace(`${redirectTo}?callbackUrl=${callbackUrl}`)
      return
    }
  }, [isAuthenticated, isLoading, token, router, redirectTo])

  return {
    isAuthenticated,
    isLoading,
    shouldRender: !isLoading && isAuthenticated
  }
}
