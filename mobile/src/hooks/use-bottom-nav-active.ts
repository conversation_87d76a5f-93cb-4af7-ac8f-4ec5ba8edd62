// hooks/useBottomNavActive.ts
'use client';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { CaseType } from '@/types/enums';

// 导航键类型定义
const NAV_KEYS = ['bazi', 'ziwei', 'meihua', 'divinate', 'user'] as const;
type NavKey = (typeof NAV_KEYS)[number];

// 解析路径名，返回导航键和分类
function parsePathname(pathname: string): [NavKey | null, CaseType | null] {
  const pathnameKeys = pathname.split('/');
  
  // 检查pathnameKeys[1]是否匹配CaseType中的某个值
  const categoryKey = pathnameKeys[1];
  let category: CaseType | null = null;
  
  // 遍历CaseType枚举值，找到匹配的分类
  if (categoryKey) {
    if (Object.values(CaseType).includes(categoryKey as CaseType)) {
      category = categoryKey as CaseType;
    }
  }
  
  if (pathnameKeys.length > 2) {
    const tab = pathnameKeys[2];
    if (NAV_KEYS.includes(tab as NavKey)) {
      return [tab as NavKey, category];
    }
  }
  
  // 如果分类本身就是导航键，则返回分类作为导航键
  const isCategoryNavKey = NAV_KEYS.includes(category as NavKey);
  return [isCategoryNavKey ? (category as NavKey) : null, category];
}

export function useBottomNavActive() {
  const pathname = usePathname();
  const router = useRouter();

  const [pendingNav, setPendingNav] = useState<NavKey | null>(null);
  const [currentNav, setCurrentNav] = useState<NavKey | null>(() => {
    return parsePathname(pathname)[0];
  });
  const [currentCategory, setCurrentCategory] = useState<CaseType | null>(() => {
    return parsePathname(pathname)[1];
  });

  // 监听路径变化，更新当前导航状态
  useEffect(() => {
    const [activeTab, category] = parsePathname(pathname);
    if (activeTab) {
      setCurrentNav(activeTab);
      setCurrentCategory(category);
      setPendingNav(null);
    }
  }, [pathname]);

  // 处理导航点击事件
  const handleNavClick = (key: NavKey) => {
    // 如果当前路径完全等于导航键，则跳过导航
    if (key === currentNav && pathname === `/${key}`) {
      return;
    }

    setPendingNav(key);

    if (key === 'divinate') {
      // 添加案例的特殊处理
      try {
        const lastVisitedTab = typeof window !== 'undefined' 
          ? localStorage.getItem('lastVisitedTab') 
          : null;
        
        if (lastVisitedTab && ['bazi', 'ziwei', 'meihua'].includes(lastVisitedTab)) {
          router.push(`/${lastVisitedTab}/divinate`);
        } else {
          router.push('/bazi/divinate');
        }
      } catch (e) {
        router.push('/bazi/divinate');
      }
    } else if (['bazi', 'ziwei', 'meihua'].includes(key)) {
      // 保存用户最后访问的tab到localStorage
      try {
        if (typeof window !== 'undefined') {
          localStorage.setItem('lastVisitedTab', key);
        }
        router.push(`/${key}/case`);
      } catch (e) {
        router.push(`/${key}/case`);
      }
    } else if (key === 'user') {
      router.push('/user');
    }
  };

  // 当前激活的导航项（优先显示待处理的导航）
  const activeNav = pendingNav || currentNav;

  return { activeNav, currentCategory, handleNavClick };
}
