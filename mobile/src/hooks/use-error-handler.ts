"use client"

import { useCallback } from 'react'
import { Toast } from 'antd-mobile'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication', 
  SYNTAX = 'syntax',
  RESOURCE = 'resource',
  RUNTIME = 'runtime',
  API_SERVER = 'api_server',
  UNKNOWN = 'unknown'
}

/**
 * 错误信息配置
 */
const ERROR_MESSAGES = {
  [ErrorType.NETWORK]: '网络连接异常，请检查网络设置',
  [ErrorType.AUTHENTICATION]: '登录状态已过期，请重新登录',
  [ErrorType.SYNTAX]: '页面资源异常，正在尝试修复...',
  [ErrorType.RESOURCE]: '资源加载失败，请刷新页面重试',
  [ErrorType.RUNTIME]: '程序运行出现异常，请稍后重试',
  [ErrorType.API_SERVER]: '服务器处理请求时发生错误，请稍后重试',
  [ErrorType.UNKNOWN]: '发生了未知错误，请稍后重试'
}

/**
 * 错误处理选项
 */
interface ErrorHandlerOptions {
  /** 是否显示 Toast */
  showToast?: boolean
  /** 自定义错误消息 */
  customMessage?: string
  /** Toast 显示时长 */
  duration?: number
  /** 是否在控制台输出详细错误 */
  logToConsole?: boolean
}

/**
 * 全局错误处理 Hook
 */
export function useErrorHandler() {
  
  /**
   * 根据错误内容判断错误类型
   */
  const classifyError = useCallback((error: any): ErrorType => {
    if (!error) return ErrorType.UNKNOWN
    
    const message = error.message || error.toString()
    
    // API 服务器错误
    if (message.includes('500') || 
        message.includes('Internal Server Error') ||
        message.includes('502') ||
        message.includes('503') ||
        message.includes('504') ||
        message.includes('Bad Gateway') ||
        message.includes('Service Unavailable') ||
        message.includes('Gateway Timeout')) {
      return ErrorType.API_SERVER
    }
    
    // 网络错误
    if (message.includes('fetch') || 
        message.includes('Network') || 
        message.includes('Failed to fetch') ||
        message.includes('ERR_NETWORK') ||
        message.includes('ERR_CONNECTION') ||
        message.includes('ECONNREFUSED')) {
      return ErrorType.NETWORK
    }
    
    // 认证错误
    if (message.includes('401') || 
        message.includes('Unauthorized') ||
        message.includes('403') ||
        message.includes('Forbidden') ||
        error.name === 'UnauthorizedError') {
      return ErrorType.AUTHENTICATION
    }
    
    // React 渲染错误 (特别处理)
    if (message.includes('Objects are not valid as a React child') ||
        message.includes('Cannot read property') ||
        message.includes('Cannot read properties') ||
        message.includes('is not a function')) {
      return ErrorType.RUNTIME
    }
    
    // 语法错误
    if (message.includes('Unexpected token') || 
        message.includes('SyntaxError') ||
        message.includes('Unexpected end of JSON input')) {
      return ErrorType.SYNTAX
    }
    
    // 资源加载错误
    if (message.includes('Loading chunk') ||
        message.includes('ChunkLoadError') ||
        message.includes('script') ||
        message.includes('stylesheet')) {
      return ErrorType.RESOURCE
    }
    
    // 运行时错误
    if (error instanceof TypeError ||
        error instanceof ReferenceError ||
        error instanceof RangeError) {
      return ErrorType.RUNTIME
    }
    
    return ErrorType.UNKNOWN
  }, [])

  /**
   * 处理错误
   */
  const handleError = useCallback((
    error: any, 
    options: ErrorHandlerOptions = {}
  ) => {
    const {
      showToast = true,
      customMessage,
      duration = 3000,
      logToConsole = process.env.NODE_ENV === 'development'
    } = options
    
    // 如果传入 null 或 undefined，但有自定义消息，则只显示自定义消息
    if (!error && customMessage) {
      if (showToast) {
        Toast.show({
          content: customMessage,
          duration: duration / 1000,
          position: 'top'
        })
      }
      
      return {
        type: ErrorType.UNKNOWN,
        message: customMessage,
        originalError: null
      }
    }
    
    // 如果没有错误也没有自定义消息，直接返回
    if (!error && !customMessage) {
      return {
        type: ErrorType.UNKNOWN,
        message: ERROR_MESSAGES[ErrorType.UNKNOWN],
        originalError: null
      }
    }
    
    // 在开发环境或明确要求时输出到控制台
    if (logToConsole && error) {
      console.error('错误详情:', error)
    }
    
    // 分类错误
    const errorType = classifyError(error)
    
    // 显示用户友好的错误提示
    if (showToast) {
      const message = customMessage || ERROR_MESSAGES[errorType]
      
      Toast.show({
        content: message,
        duration: duration / 1000, // antd-mobile 使用秒为单位
        position: 'top'
      })
    }
    
    return {
      type: errorType,
      message: customMessage || ERROR_MESSAGES[errorType],
      originalError: error
    }
  }, [classifyError])

  /**
   * 处理网络错误
   */
  const handleNetworkError = useCallback((error: any, customMessage?: string) => {
    return handleError(error, {
      customMessage: customMessage || '网络请求失败，请检查网络连接',
      showToast: true
    })
  }, [handleError])

  /**
   * 处理认证错误
   */
  const handleAuthError = useCallback((error: any, customMessage?: string) => {
    return handleError(error, {
      customMessage: customMessage || '登录已过期，即将跳转到登录页面',
      showToast: true,
      duration: 2000
    })
  }, [handleError])

  /**
   * 处理 API 服务器错误
   */
  const handleApiError = useCallback((error: any, customMessage?: string) => {
    return handleError(error, {
      customMessage: customMessage || '服务器暂时无法处理您的请求，请稍后重试',
      showToast: true,
      duration: 4000
    })
  }, [handleError])

  /**
   * 处理静默错误（不显示 Toast）
   */
  const handleSilentError = useCallback((error: any) => {
    return handleError(error, {
      showToast: false,
      logToConsole: true
    })
  }, [handleError])

  return {
    handleError,
    handleNetworkError,
    handleAuthError,
    handleApiError,
    handleSilentError,
    classifyError,
    ErrorType
  }
}