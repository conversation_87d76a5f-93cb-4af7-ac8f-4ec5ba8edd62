'use client'

import { useRef, MouseEvent, TouchEvent, useCallback } from 'react'

interface UseLongPressOptions {
    threshold?: number
    onClick?: (id: string) => void
    onLongPress?: (id: string) => void
}

export function useLongPress({
    threshold = 600,
    onClick,
    onLongPress
}: UseLongPressOptions) {
    const timerRef = useRef<NodeJS.Timeout | null>(null)
    const isLongPressTriggered = useRef(false)
    const targetItemId = useRef<string | null>(null)
    const startPosition = useRef<{ x: number; y: number } | null>(null)
    const isScrolling = useRef(false)

    // 开始按下
    const handlePressStart =
        (e: MouseEvent | TouchEvent) => {
            console.log('handlePressStart  start')
            const targetElement = (e.target as HTMLElement).closest('[data-item-id]')
            if (!targetElement) return

            const id = targetElement.getAttribute('data-item-id')
            if (!id) return

            // 记录开始位置
            if ('touches' in e) {
                startPosition.current = {
                    x: e.touches[0].clientX,
                    y: e.touches[0].clientY
                }
            } else {
                startPosition.current = {
                    x: e.clientX,
                    y: e.clientY
                }
            }

            targetItemId.current = id
            isLongPressTriggered.current = false
            isScrolling.current = false

            timerRef.current = setTimeout(() => {
                // 只有在没有滚动的情况下才触发长按
                if (!isScrolling.current) {
                    isLongPressTriggered.current = true
                    onLongPress?.(id)
                }
            }, threshold)
        }

    // 松开
    const handlePressEnd = (event: MouseEvent | TouchEvent) => {
        if (timerRef.current) {
            clearTimeout(timerRef.current)
        }

        // 如果是长按触发的，就在事件结束时阻止默认行为（如移动端菜单）
        if (isLongPressTriggered.current) {
            // 检查事件是否可以取消，避免 Intervention 错误
            if (event.cancelable) {
                event.preventDefault();
            }
        }

        // 只有在没有滚动的情况下才触发点击
        if (!isLongPressTriggered.current && targetItemId.current && !isScrolling.current) {
            onClick?.(targetItemId.current)
        }

        // 重置状态
        targetItemId.current = null
        startPosition.current = null
        isScrolling.current = false
    }

    // 移动处理 - 区分滚动和移出
    const handlePressMove = (e: TouchEvent) => {
        if (!startPosition.current) return

        const currentTouch = e.touches[0]
        const deltaX = Math.abs(currentTouch.clientX - startPosition.current.x)
        const deltaY = Math.abs(currentTouch.clientY - startPosition.current.y)

        // 如果主要是垂直移动且移动距离超过5px，认为是滚动
        if (deltaY > deltaX && deltaY > 5) {
            isScrolling.current = true
            // 取消长按定时器，但不阻止滚动
            if (timerRef.current) {
                clearTimeout(timerRef.current)
                timerRef.current = null
            }
            // 不调用 preventDefault()，允许滚动继续
            return
        }

        // 如果移动距离超过10px，取消长按
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
        if (distance > 10) {
            if (timerRef.current) {
                clearTimeout(timerRef.current)
                timerRef.current = null
            }
            targetItemId.current = null
            startPosition.current = null
        }
    }

    // 鼠标离开处理
    const handleMouseLeave = () => {
        if (timerRef.current) {
            clearTimeout(timerRef.current)
        }
        targetItemId.current = null
        startPosition.current = null
        isScrolling.current = false
    }

    // 关键：阻止浏览器默认的长按菜单
    const handleContextMenu = (e: MouseEvent) => {
        e.preventDefault()
    }


    return {
        bind: {
            onMouseDown: handlePressStart,
            onTouchStart: handlePressStart,
            onMouseUp: handlePressEnd,
            onTouchEnd: handlePressEnd,
            onTouchMove: handlePressMove,
            onMouseLeave: handleMouseLeave,
            onContextMenu: handleContextMenu,
        },
    }
}