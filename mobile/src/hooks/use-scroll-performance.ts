"use client"

import { useEffect, useRef, useCallback } from 'react'

interface ScrollPerformanceOptions {
  enabled?: boolean
  logInterval?: number
  threshold?: number
}

/**
 * 滚动性能监控Hook
 * 用于调试和优化滚动性能
 */
export function useScrollPerformance(options: ScrollPerformanceOptions = {}) {
  const {
    enabled = process.env.NODE_ENV === 'development',
    logInterval = 1000,
    threshold = 16.67 // 60fps threshold
  } = options

  const frameTimeRef = useRef<number[]>([])
  const lastLogTimeRef = useRef<number>(0)
  const rafIdRef = useRef<number>()

  const measureFrame = useCallback(() => {
    const now = performance.now()
    
    if (frameTimeRef.current.length > 0) {
      const lastFrame = frameTimeRef.current[frameTimeRef.current.length - 1]
      const frameTime = now - lastFrame
      
      // 记录帧时间
      frameTimeRef.current.push(now)
      
      // 保持最近100帧的数据
      if (frameTimeRef.current.length > 100) {
        frameTimeRef.current.shift()
      }
      
      // 定期输出性能统计
      if (now - lastLogTimeRef.current > logInterval) {
        const frameTimes = frameTimeRef.current.slice(1).map((time, index) => 
          time - frameTimeRef.current[index]
        )
        
        if (frameTimes.length > 0) {
          const avgFrameTime = frameTimes.reduce((a, b) => a + b, 0) / frameTimes.length
          const maxFrameTime = Math.max(...frameTimes)
          const droppedFrames = frameTimes.filter(time => time > threshold).length
          const fps = 1000 / avgFrameTime
          
          console.log('📊 滚动性能统计:', {
            平均FPS: Math.round(fps),
            平均帧时间: Math.round(avgFrameTime * 100) / 100 + 'ms',
            最大帧时间: Math.round(maxFrameTime * 100) / 100 + 'ms',
            掉帧数: droppedFrames,
            掉帧率: Math.round((droppedFrames / frameTimes.length) * 100) + '%'
          })
        }
        
        lastLogTimeRef.current = now
      }
    } else {
      frameTimeRef.current.push(now)
    }
    
    if (enabled) {
      rafIdRef.current = requestAnimationFrame(measureFrame)
    }
  }, [enabled, logInterval, threshold])

  useEffect(() => {
    if (enabled) {
      rafIdRef.current = requestAnimationFrame(measureFrame)
      
      return () => {
        if (rafIdRef.current) {
          cancelAnimationFrame(rafIdRef.current)
        }
      }
    }
  }, [enabled, measureFrame])

  const startMonitoring = useCallback(() => {
    if (!rafIdRef.current) {
      rafIdRef.current = requestAnimationFrame(measureFrame)
    }
  }, [measureFrame])

  const stopMonitoring = useCallback(() => {
    if (rafIdRef.current) {
      cancelAnimationFrame(rafIdRef.current)
      rafIdRef.current = undefined
    }
  }, [])

  return {
    startMonitoring,
    stopMonitoring,
    isMonitoring: !!rafIdRef.current
  }
}
