"use client"
import { CaseCreateRequest, CaseUpdateRequest } from '@/types/case';
import { AppError, normalizeError } from '../error';
import FunctionalAstrolabe from "iztro/lib/astro/FunctionalAstrolabe";

// API客户端配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// 获取本地存储的认证token
function getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth_token');
}

// 清除认证信息（不进行跳转）
function clearAuthData() {
    if (typeof window !== 'undefined') {
        // 清除本地存储的认证信息
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('auth_expiry');
        localStorage.removeItem('auth_user');
    }
}

// 通用请求函数 - 增强版，支持自动刷新和重试
async function request<T>(
    endpoint: string,
    options: RequestInit = {},
    token?: string | boolean
): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const context = `API请求: ${options.method || 'GET'} ${endpoint}`;

    const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...(options.headers as Record<string, string> || {}),
    };

    // 智能token处理：支持传递token或自动获取
    let authToken: string | null = null;
    if (token === true) {
        // 自动获取token
        authToken = getAuthToken();
    } else if (typeof token === 'string') {
        // 使用传递的token
        authToken = token;
    }

    if (authToken) {
        headers.Authorization = `Bearer ${authToken}`;
    }

    try {
        const response = await fetch(url, {
            ...options,
            headers,
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            const message = errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`;

            // 处理401未授权错误
            if (response.status === 401) {
                console.log('收到401错误，清除认证信息...');
                clearAuthData();
            }
            throw new AppError('api', message, { status: response.status });

        }

        // 处理不同类型的响应
        const contentType = response.headers.get('content-type');
        let responseData: any;

        if (contentType && contentType.includes('application/json')) {
            responseData = await response.json();
        } else {
            responseData = await response.text();
        }
        return responseData;
    } catch (error) {
        throw normalizeError(error);
    }
}

// HTTP方法函数 - 支持灵活的token传递
export async function get<T>(
    endpoint: string,
    params?: Record<string, string>,
    token?: string | boolean
): Promise<T> {
    const searchParams = params ? `?${new URLSearchParams(params)}` : '';
    return request<T>(`${endpoint}${searchParams}`, { method: 'GET' }, token);
}

export async function post<T>(
    endpoint: string,
    data?: any,
    token?: string | boolean
): Promise<T> {
    return request<T>(endpoint, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
    }, token);
}

export async function put<T>(
    endpoint: string,
    data?: any,
    token?: string | boolean
): Promise<T> {
    return request<T>(endpoint, {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
    }, token);
}

export async function del<T>(
    endpoint: string,
    token?: string | boolean
): Promise<T> {
    return request<T>(endpoint, { method: 'DELETE' }, token);
}

// SWR fetcher函数 - 支持认证和无认证
export const createFetcher = (token?: string | boolean) => (url: string) => get(url, undefined, token);

// ========================= API分组定义 =========================

// 认证相关API
export const authAPI = {
    // 登录
    async login(credentials: { username: string; password: string; login_type: string }) {
        return post<{
            access_token: string
            user: {
                id: string
                name: string
                email?: string
                phone?: string
                role?: string
                expiresAt?: number
            }
        }>('/auth/login/json', credentials);
    },

    // 注册  
    async register(data: { email?: string; phone?: string; password: string }) {
        return post<{ message: string }>('/auth/register', data);
    },

    // 刷新Token
    async refreshToken(token: string) {
        return post<{ access_token: string }>('/auth/refresh', {}, token);
    },

    // 登出
    async logout(token: string) {
        return post('/auth/logout', {}, token);
    }
};

// 书籍相关API - 统一命名
export const bookAPI = {
    // 获取书籍列表
    async getBooks(params: {
        page?: string
        size?: string
        is_completed?: string
        category?: string | null
    } = {}) {
        const requestParams = {
            page: params.page || '1',
            size: params.size || '10',
            is_completed: params.is_completed || 'true',
            ...(params.category && { category: params.category }),
        };
        return get<{
            items: any[]
            total: number
            page: number
            page_size: number
            total_pages: number
        }>('/book/list', requestParams);
    },

    // 获取书籍详情
    async getBookDetail(book_id: string | null, token?: string | boolean) {
        if (!book_id) {
            return null;
        }
        return get<{
            chapter_list: any[]
            content?: any
        }>(`/book/${book_id}`, undefined, token || false);
    },

    // 获取章节内容
    async getChapterContent(chapter_id: string | null, token?: string | boolean) {
        if (!chapter_id) {
            return null;
        }
        return get<{
            chapters: any
        }>(`/book/chapter/${chapter_id}`, undefined, token || false);
    }
};

export const divinateAPI = {
    // 紫微斗数测算
    divinateAnalysis: async (data: FunctionalAstrolabe, token?: string) => {
        return post<string>('/ziwei/divinate/analysis', data, token);
    }
}

// 案例相关API
export const caseAPI = {
    // 创建案例
    createCase: async (data: CaseCreateRequest, token?: string) => {
        if (!token) {
            return null;
        }
        console.log('createCase', data)
        return post<string>('/case/new', data, token);
    },

    // 更新案例
    updateCase: async (caseId: string, data: CaseUpdateRequest, token?: string) => {
        if (!token) {
            return null;
        }
        // 匹配后端路径 /update/{case_id}
        const result = await put<{ success: boolean; message: string }>(`/case/update/${caseId}`, data, token);
        return result;
    },

    // 获取案例详情
    getCaseDetail: async (caseId: string, token?: string) => {
        return get<any>(`/case/${caseId}`, undefined, token);
    },

    // 获取案例列表
    getCases: async (params: any, token?: string) => {
        return get<{
            items: any[]
            total: number
            page: number
            page_size: number
        }>(`/case/list`, params, token);
    },

    // 删除案例
    deleteCase: async (caseId: string, token?: string) => {
        if (!token) {
            return null;
        }
        console.log('deleteCase', caseId)
        return del<{ success: boolean; message: string }>(`/case/del/${caseId}`, token);
    },
}