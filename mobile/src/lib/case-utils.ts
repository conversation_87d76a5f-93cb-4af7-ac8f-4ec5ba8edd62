import { BirthData } from '@/types/user'
import { CaseResponse } from '@/types/case'
import { convertISOToDateTimePickerFormat } from './utils'


// 从CaseResponse构建BirthData的转换函数
export function buildBirthDataFromCase(caseDetail: CaseResponse): BirthData {
  // 性别转换：将中文性别转换为英文
  const genderMap: Record<string, "male" | "female"> = {
    'male': 'male',
    'female': 'female',
    '男': 'male',
    '女': 'female'
  }
  
  const gender = genderMap[caseDetail.gender] || 'male'
  
  // 关系类型转换：将CaseResponse中的relation_type转换为BirthData中的relationship
  const relationshipMap: Record<string, BirthData['relationship']> = {
    'me': 'me',
    'family': 'family', 
    'classmate': 'classmate',
    'friend': 'friend',
    'colleague': 'colleague',
    'other': 'other',
    'celebrity': 'celebrity'
  }
  
  const relationship = relationshipMap[caseDetail.relation_type] || 'other'
  
  
  // 构建BirthData对象
  const birthData: BirthData = {
    name: caseDetail.user_name,
    gender,
    birthTime: convertISOToDateTimePickerFormat(caseDetail.birth_time_solar), // 转换ISO格式为DateTimePicker格式
    isLunar: false, // CaseResponse中的birth_time_solar是公历时间，所以isLunar为false
    useTrueSolarTime: caseDetail.useTrueSolarTime,
    isDST: caseDetail.isDST,
    useEarlyOrLateNight: caseDetail.useEarlyOrLateNight,
    birthplace: caseDetail.birth_place,
    longitude: caseDetail.longitude,
    relationship
  }
  
  return birthData
}

// 验证CaseResponse数据是否完整
export function validateCaseForBirthData(caseDetail: CaseResponse): boolean {
  const requiredFields: (keyof CaseResponse)[] = [
    'user_name',
    'gender', 
    'birth_time_solar',
    'birth_place',
    'longitude',
    'relation_type',
    'useTrueSolarTime',
    'isDST',
    'useEarlyOrLateNight'
  ]
  
  return requiredFields.every(field => caseDetail[field] !== undefined && caseDetail[field] !== null)
}

// 从CaseResponse构建submitID（使用case_id）
export function buildSubmitIDFromCase(caseDetail: CaseResponse): string {
  return caseDetail.case_id
}

// 检查案例是否应该保存（通常从案例详情页面查看时不需要保存）
export function shouldSaveCaseFromDetail(): boolean {
  return false
}

// 安全获取测算结果中的值
export function getDivinateResultValue(result: Record<string, any> | null | undefined, key: string, defaultValue: any = null): any {
  if (!result || typeof result !== 'object') {
    return defaultValue
  }
  return result[key] !== undefined ? result[key] : defaultValue
}

// 检查测算结果是否为空
export function isDivinateResultEmpty(result: Record<string, any> | null | undefined): boolean {
  return !result || Object.keys(result).length === 0
}
