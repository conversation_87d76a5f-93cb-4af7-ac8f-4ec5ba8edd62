// lib/errors.ts

export type AppErrorType = 'network' | 'api' | 'parse' | 'auth' |'runtime' | 'unknown';

export class AppError extends Error {
    type: AppErrorType;
    status?: number;       // HTTP 状态码（仅 API 错误有）
    userMessage: string;   // 用户可见的提示
    original?: unknown;    // 原始错误对象
    code?: string;         // 后端返回的业务错误码（可选）
  
    constructor(
      type: AppErrorType,
      message: string,
      options?: {
        userMessage?: string;
        status?: number;
        original?: unknown;
        code?: string;
      }
    ) {
      super(message);
      this.type = type;
      this.status = options?.status;
      this.original = options?.original;
      this.code = options?.code;
      this.userMessage = options?.userMessage ?? message;
    }
  }
/**
 * 更健壮的 normalizeError
 */
export function normalizeError(e: unknown): AppError {
  if (e instanceof AppError) return e;

  // 网络错误（fetch / offline）
  if (
    e instanceof TypeError &&
    (e.message.includes('fetch') ||
      e.message.includes('NetworkError') ||
      e.message.includes('Failed to fetch'))
  ) {
    return new AppError('network', e.message, {
      original: e,
      userMessage: '网络连接异常，请检查您的网络',
    });
  }

  // API 错误（可能是后端返回 status code 或者包含 error 字段）
  if (e instanceof Response) {
    if (e.status === 401) {
      return new AppError('auth', `API 请求失败: ${e.status}`, {
        original: e,
        userMessage: '用户权限有问题，请重新登录',
      });
    }
    return new AppError('api', `API 请求失败: ${e.status}`, {
      original: e,
      userMessage: '服务暂时不可用，请稍后重试',
    });
  }

  // 解析错误（JSON.parse / response.json 失败）
  if (e instanceof SyntaxError && e.message.includes('JSON')) {
    return new AppError('parse', e.message, {
      original: e,
      userMessage: '数据解析失败，请稍后重试',
    });
  }

  // 默认 → 运行时错误
  return new AppError(
    'runtime',
    e instanceof Error ? e.message : String(e),
    {
      original: e,
      userMessage: '出错了，请稍后重试',
    }
  );
}