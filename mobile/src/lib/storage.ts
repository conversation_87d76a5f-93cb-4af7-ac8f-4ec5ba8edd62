"use client"

// 保存新的 submissionId，限制最多3个
export function saveArrayToSessionStorage(key: string, value: string, maxLength: number = 3) {
  // 读取已有记录（解析成数组）
  const stored = JSON.parse(sessionStorage.getItem(key) || '[]') as string[];

  // 添加新ID到末尾
  stored.push(value);

  // 限制长度最多3个
  if (stored.length > maxLength) {
    stored.shift(); // 删除第一个（最旧的）
  }

  // 存回 sessionStorage
  sessionStorage.setItem(key, JSON.stringify(stored));
}

// 检查是否已保存过
export function hasValueInSessionStorage(key: string, value: string) {
  const stored = JSON.parse(sessionStorage.getItem(key) || '[]') as string[];
  return stored.includes(value);
}
