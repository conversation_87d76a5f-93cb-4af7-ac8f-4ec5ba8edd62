import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
// import { ReadonlyURLSearchParams } from "next/navigation"

let callId = 0;
export function createLogger(prefix = '') {
  const id = ++callId; // 每次调用生成一个新 ID
  const traceId = `${prefix}${id}`.trim();

  return {
    log: (...args: any[]) => console.log(`[${traceId}]`, ...args),
    warn: (...args: any[]) => console.warn(`[${traceId}]`, ...args),
    error: (...args: any[]) => console.error(`[${traceId}]`, ...args),
  };
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 获取用户名首字母
export function getInitials(name: string) {
  return name
    .split(" ")
    .map((part) => part[0])
    .join("")
    .toUpperCase()
}

// export function getCurrentUrl(pathname: string, searchParams: ReadonlyURLSearchParams) {
//   let currentUrl = pathname
//   try {
//     const params = searchParams.toString()
//     if (params) {
//       currentUrl = `${pathname}?${params}`
//     }
//   } catch (error) {
//     console.warn('获取搜索参数失败:', error)
//   }
//   return currentUrl
// }

export function formatDate(date: Date | undefined) {
  if (!date) {
    return ""
  }
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  })
}

export function isValidDate(date: Date | undefined) {
  if (!date) {
    return false
  }
  //需要优化格式
  console.log('isValidDate:', date, !isNaN(date.getTime()))
  return !isNaN(date.getTime())
}

// 将ISO格式的datetime转换为DateTimePicker需要的格式
export function convertISOToDateTimePickerFormat(isoString: string): string {
  try {
    // 移除秒和毫秒部分，只保留到分钟
    // "1995-02-02T07:26:00" -> "1995-02-02 07:26"
    return isoString.replace('T', ' ').substring(0, 16)
  } catch (error) {
    console.warn('Failed to convert ISO datetime format:', error)
    return isoString
  }
}