import { CaseType, RelationType } from './enums'

// CaseResponse类型定义，与后端case_schema.py保持一致
export interface CaseResponse {
  case_id: string
  user_id: string
  user_name: string
  gender: string
  birth_time_solar: string // 公历出生时间，格式: "2025-06-05 12:00:00"
  birth_time_lunar: string // 农历出生时间，格式: "2025年六月初五子时"
  
  // 四柱
  bazi_year: string
  bazi_month: string
  bazi_day: string
  bazi_time: string
  
  birth_place: string // 出生地点，格式: "北京,北京,东城区"
  
  // 时间设置
  useTrueSolarTime: boolean
  isDST: boolean
  useEarlyOrLateNight: boolean
  longitude: number
  
  // 测算结果和备注 - 统一使用JSON类型
  divinate_result?: Record<string, any> | null
  comment?: string
  
  // 关系类型和标签
  relation_type: RelationType
  tags: string[]
  case_type: CaseType
  
  // 时间戳
  created_at: string
  updated_at: string
}

// CaseCreate类型定义
export interface CaseCreateRequest {
  user_name: string
  gender: string
  birth_time_solar: string
  birth_time_lunar: string
  bazi_year: string
  bazi_month: string
  bazi_day: string
  bazi_time: string
  birth_place: string
  useTrueSolarTime: boolean
  isDST: boolean
  useEarlyOrLateNight: boolean
  longitude: number
  divinate_result?: Record<string, any> | null
  comment?: string
  relation_type: RelationType
  tags: string[]
  case_type: CaseType
}

// CaseUpdate类型定义
export interface CaseUpdateRequest {
  user_name?: string
  gender?: string
  birth_time_solar?: string
  birth_time_lunar?: string
  bazi_year?: string
  bazi_month?: string
  bazi_day?: string
  bazi_time?: string
  birth_place?: string
  divinate_result?: Record<string, any> | null
  comment?: string
  relation_type?: RelationType
  tags?: string[]
}

// CaseSearchParams类型定义
export interface CaseSearchParams {
  user_name?: string
  birth_time_solar?: string
  birth_time_lunar?: string
  bazi_year?: string
  bazi_month?: string
  bazi_day?: string
  bazi_time?: string
  tags?: string[]
  case_type?: CaseType
  relation_type?: RelationType
  search_text?: string
  page?: number
  page_size?: number
}

// CaseListResponse类型定义
export interface CaseListResponse {
  total: number
  items: CaseResponse[]
  page: number
  page_size: number
}


export interface CaseProfile {
  case_id: string
  user_id: string
  user_name: string
  gender: "男" | "女" | "其他" | "male" | "female"
  birth_time_solar: string | Date
  birth_time_lunar: string
  birth_place: string
  bazi_year: string
  bazi_month: string
  bazi_day: string
  bazi_time: string
  divinate_result?: any
  comment?: string
  tags: string[] // 0-3个标签，每个不超过4个字
  relation_type: RelationType
  case_type: CaseType
  created_at?: string | Date
  updated_at?: string | Date
  
  // 为了向后兼容，保留原有的id字段
  id?: string
}

// 个人档案接口 - 包含今日运势
export interface PersonalProfile extends CaseProfile {
  today_fortune?: string // 今日运势
}
