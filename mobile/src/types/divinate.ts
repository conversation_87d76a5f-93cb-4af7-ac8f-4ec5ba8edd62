import { HoroscopeItem } from 'iztro/lib/data/types'
import { Bazi } from './user'

export interface BirthTimeInfo {
  solarTime: string // 公历时间 "2019-03-04 14:00"
  lunarTime: string // 阴历时间 "二零一九年二月初七"
  trueSolarTime: string // 真太阳时, 公历时间 "2019-03-04 14:00"
  bazi: Bazi // 八字 "己亥 丙寅 戊戌 辛酉"
  divinateTime: string // 用于测算的公历时间：北京时间还是真太阳时由用户的选择决定
}

export interface ZiweiDivinateRequest {
  name: string
  gender: "male" | "female"
  // 用于测算的公历时间：北京时间还是真太阳时由用户的选择决定
  divinateDate: string
  // 时辰
  timeIndex: number
  // 阳历
  solarTime: string
  // 真太阳时
  trueSolarTime: string
}

export interface BaziResult {
  yearPillar: { heavenlyStem: string; earthlyBranch: string }
  monthPillar: { heavenlyStem: string; earthlyBranch: string }
  dayPillar: { heavenlyStem: string; earthlyBranch: string }
  hourPillar: { heavenlyStem: string; earthlyBranch: string }
  lunarDate: string
  solarDate: string
  nayin: string[]
  dayMaster: string
  wuxing: {
    wood: number
    fire: number
    earth: number
    metal: number
    water: number
  }
  analysis: {
    summary: string
    luck: string
    personality: string
    career: string
    relationships: string
    health: string
  }
}

// 运限类型枚举
export type FortuneType = '大限' | '流年' | '流月' | '流日' | '流时'

// 运限盘颜色映射 - 直接使用hex颜色值
export const FortuneColorMap: Record<FortuneType, string> = {
  '大限': '#3b82f6',    // blue-500
  '流年': '#4d7c0f',    // lime-600
  '流月': '#a16207',    // yellow-700
  '流日': '#ec4899',    // pink-700
  '流时': '#eab308'     // yellow-500
}

export const Mutagen: string[] = ['禄', '权', '科', '忌']

// 运限请求类型
export interface ZiweiFortuneRequest {
  /** 运限类型 */
  type: FortuneType
  /** 对应的日期，不能为空 */
  date: Date
  /** 流时的索引，只有在选择流时时才有值，范围0-12 */
  timeIndex?: number
}

// 运限盘数据
export interface FortuneData {
  type: string
  date: string
  time: number
  fortuneData: HoroscopeItem[]
  fortuneColor: string[]
  palaceNames: string[][]
  mutagens: Record<string, string>[]
  yearlyDecStars: string[][]  // 流年大限盘的十二星, [[jiangqian, suiqian]]
}
