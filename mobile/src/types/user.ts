export interface User {
  id: string
  name: string
  email?: string
  phone?: string
  role?: string
  expiresAt?: number
}

export interface LoginCredentials {
  loginType: "email" | "phone"
  loginId: string
  password: string
} 

export interface LocationItem {
  value: string
  label: string
}

export interface Bazi {
  year: string
  month: string
  day: string
  time: string
}

export interface BirthData {
  name: string
  gender: "male" | "female"
  birthTime: string  // 格式: "YYYY-MM-DD HH:mm"
  // 是否是农历
  isLunar: boolean
  // 是否使用真太阳时
  useTrueSolarTime: boolean
  // 是否是夏令时
  isDST: boolean
  // 是否是早晚子时
  useEarlyOrLateNight: boolean
  // 出生地
  birthplace: string
  // 经度
  longitude: number
  // 关系
  relationship: "me" | "family" | "classmate" | "friend" | "colleague" | "other" | "celebrity"
}