from typing import Any
import math
from fastapi import Request, HTTPException
from fastapi import APIRouter, Depends, Query, Path
from app.dependencies.service import get_book_service, get_current_user_info
from app.services.book_service import BookService
from app.schemas.auth_schemas import CurrentUser
from app.schemas.book_schemas import (
    BookListResponse,
    BookMainResponse,
    BookChapterContentResponse,
)

router = APIRouter()

@router.get("/debug")
async def debug(request: Request):
    print(request.headers)

@router.get("/list", response_model=BookListResponse)
async def list_books(
    category: str = Query(None, description="书籍类目"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    is_completed: bool = Query(True, description="是否已发布"),
    book_service: BookService = Depends(get_book_service),
    #current_user: CurrentUser = Depends(get_current_user_info)
) -> Any:
    """
    获取书籍列表
    """
    try:
        skip = (page - 1) * size
        books = await book_service.list_books(
            skip=skip,
            limit=size,
            category=category,
            is_completed=is_completed,
            current_user=None
        )
        total = len(books)  # 这里应该从 service 层获取总数
        total_pages = math.ceil(total / size) if total > 0 else 0
        
        return BookListResponse(
            items=books,
            total=total,
            page=page,
            page_size=size,
            total_pages=total_pages
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e

@router.get("/{book_id}", response_model=BookMainResponse)
async def get_book_main_page(
    book_id: str = Path(..., description="书籍ID"),
    book_service: BookService = Depends(get_book_service),
    # current_user: CurrentUser = Depends(get_current_user_info)
) -> Any:
    """
    获取书籍章节列表和第一章节内容
    """
    try:
        #book_info = await book_service.get_book_info(book_id=book_id, current_user=current_user)
        #print(book_info)
        current_user = None
        chapter_list = await book_service.get_chapter_list(book_id=book_id, current_user=current_user)
        
        if chapter_list and len(chapter_list) > 0:
            first_chapter_id = chapter_list[0].chapter_id
            content = await book_service.get_chapter_content(chapter_id=first_chapter_id, 
                                                             current_user=current_user,
                                                             is_only_first_sub=True)
        else:
            content = None

        return BookMainResponse(
            #book_info=book_info,
            chapter_list=chapter_list,
            content=content
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e

@router.get("/chapter/{chapter_id}", response_model=BookChapterContentResponse)
async def get_book_chapter_content(
    chapter_id: str = Path(..., description="章节ID"),
    book_service: BookService = Depends(get_book_service),
    #current_user: CurrentUser = Depends(get_current_user_info)
) -> Any:
    """
    获取指定章节内容
    """
    current_user = None
    try:
        chapter_content = await book_service.get_chapter_content(chapter_id=chapter_id, current_user=current_user)
        return BookChapterContentResponse(
            chapters=chapter_content
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e
