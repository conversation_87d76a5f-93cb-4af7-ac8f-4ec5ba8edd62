from typing import Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from app.dependencies.service import get_case_service, get_current_user_info
from app.services.case_service import CaseService
from app.schemas.auth_schemas import CurrentUser
from app.schemas.case_schema import (
    CaseCreate,
    CaseUpdate,
    CaseResponse,
    CaseListResponse,
    CaseSearchParams
)

router = APIRouter()

@router.post("/new")
async def create_case(
    case_data: CaseCreate,
    case_service: CaseService = Depends(get_case_service),
    current_user: Optional[CurrentUser] = Depends(get_current_user_info)
) -> str:
    """
    创建新的案例
    """
    try:
        case_id = await case_service.create_case(
            case_data=case_data,
            current_user=current_user
        )
        if not case_id:
            raise HTTPException(status_code=400, detail={"message": "创建案例失败"})
        return case_id
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e

@router.put("/update/{case_id}")
async def update_case(
    case_id: str = Path(..., description="案例ID"),
    case_update: CaseUpdate = None,
    update_reason: Optional[str] = Query(None, description="更新原因"),
    case_service: CaseService = Depends(get_case_service),
    current_user: Optional[CurrentUser] = Depends(get_current_user_info)
) -> dict:
    """
    更新案例信息
    """
    try:
        case = await case_service.update_case(
            case_id=case_id,
            case_update=case_update,
            update_reason=update_reason,
            current_user=current_user
        )
        if not case:
            raise HTTPException(status_code=404, detail={"message": "案例不存在"})
        return {"success": True, "message": "更新成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail={"message": str(e)}) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e

@router.get("/list", response_model=CaseListResponse)
async def search_cases(
    search_params: CaseSearchParams = Depends(),
    case_service: CaseService = Depends(get_case_service),
    current_user: Optional[CurrentUser] = Depends(get_current_user_info)
) -> Any:
    """
    搜索案例
    """
    
    try:
        items, total = await case_service.search_cases(
            params=search_params,
            current_user=current_user
        )
        return CaseListResponse(
            items=items,
            total=total,
            page=search_params.page,
            page_size=search_params.page_size
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e

@router.get("/history/{case_id}")
async def get_case_history(
    case_id: str = Path(..., description="案例ID"),
    case_service: CaseService = Depends(get_case_service),
    current_user: Optional[CurrentUser] = Depends(get_current_user_info)
) -> Any:
    """
    获取案例的历史记录
    """
    try:
        history = await case_service.get_case_history(
            case_id=case_id,
            current_user=current_user
        )
        if not history:
            raise HTTPException(status_code=404, detail={"message": "没有历史记录"})
        return history
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e

@router.delete("/del/{case_id}")
async def delete_case(
    case_id: str = Path(..., description="案例ID"),
    case_service: CaseService = Depends(get_case_service),
    current_user: Optional[CurrentUser] = Depends(get_current_user_info)
) -> dict:
    """
    删除指定案例
    """
    try:
        if not current_user:
            raise HTTPException(status_code=401, detail={"message": "用户未登录"})
        
        success = await case_service.delete_case(
            case_id=case_id,
            current_user=current_user
        )
        if not success:
            raise HTTPException(status_code=404, detail={"message": "案例不存在或无权限删除"})
        
        return {"success": True, "message": "删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail={"message": str(e)}) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e

@router.get("/{case_id}", response_model=CaseResponse)
async def get_case(
    case_id: str = Path(..., description="案例ID"),
    case_service: CaseService = Depends(get_case_service),
    current_user: Optional[CurrentUser] = Depends(get_current_user_info)
) -> Any:
    """
    获取单个案例的详细信息
    """
    try:
        case = await case_service.get_case(
            case_id=case_id,
            current_user=current_user
        )
        if not case:
            raise HTTPException(status_code=404, detail={"message": "案例不存在"})
        return case
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": str(e)}) from e
