from fastapi import APIRouter
from app.api import auth, book, test, case
from app.api.ziwei import divinate as ziwei_divinate

# 创建API路由
api_router = APIRouter()

# 在这里导入并包含各个子路由

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])

# 用户相关路由
# api_router.include_router(user.router, prefix="/user", tags=["user"])

# 书籍相关路由
api_router.include_router(book.router, prefix="/book", tags=["book"])

# 案例相关路由
api_router.include_router(case.router, prefix="/case", tags=["case"])

# TODO: 添加命理学相关路由
# 八字相关路由
# api_router.include_router(bazi_router, prefix="/bazi", tags=["bazi"])

# 紫微斗数相关路由
api_router.include_router(ziwei_divinate.router, prefix="/ziwei", tags=["ziwei"])

# 梅花易数相关路由
# api_router.include_router(meihua_router, prefix="/meihua", tags=["meihua"]) 

# test路由
api_router.include_router(test.router, prefix="/test", tags=["test"])