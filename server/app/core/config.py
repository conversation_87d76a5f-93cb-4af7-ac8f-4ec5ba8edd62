import secrets
from typing import Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    """应用配置类"""
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="ignore")
    
    # 基本设置
    APP_ENV: str = "development"
    APP_NAME: str = "FateExplorer"
    DEBUG: bool = True
    SECRET_KEY: str = secrets.token_urlsafe(32)
    
    # API和认证
    API_PREFIX: str = "/api"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 1  # 1天
    
    # CORS
    CORS_ORIGINS: str = "http://localhost"
    
    # 数据库配置 - PostgreSQL
    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    POSTGRES_PORT: str = "5432"
    POSTGRES_SCHEMA: str = "public"  # 默认使用public schema
    DATABASE_URI: Optional[str] = None
    
    @field_validator("DATABASE_URI", mode="before")
    def assemble_db_connection(cls, v: Optional[str], values) -> str:   # pylint: disable=no-self-argument
        if v is not None:
            return v
        # 构建基本连接字符串
        conn_str = f"postgresql://{values.data['POSTGRES_USER']}:{values.data['POSTGRES_PASSWORD']}@{values.data['POSTGRES_SERVER']}:{values.data['POSTGRES_PORT']}/{values.data['POSTGRES_DB']}"
        return conn_str
    
    # 数据库配置 - MongoDB
    MONGODB_SERVER: str
    MONGODB_DB: str
    MONGODB_USER: str
    MONGODB_PASSWORD: str
    MONGODB_PORT: str = "27017"
    MONGODB_URI: Optional[str] = None
    MONGODB_BOOK_COLLECTION: str = "books"
    MONGODB_CASE_COLLECTION: str = "cases"
    MONGODB_CASE_HISTORY_COLLECTION: str = "case_history"
    
    @field_validator("MONGODB_URI", mode="before")
    def assemble_mongodb_connection(cls, v: Optional[str], values) -> str: # pylint: disable=no-self-argument
        if v is not None:
            return v
        return f"mongodb://{values.data['MONGODB_USER']}:{values.data['MONGODB_PASSWORD']}@{values.data['MONGODB_SERVER']}:{values.data['MONGODB_PORT']}/{values.data['MONGODB_DB']}?authSource=admin"

    # Redis配置
    REDIS_HOST: str 
    REDIS_PORT: int 
    REDIS_DB: int
    REDIS_PASSWORD: Optional[str] = None
    
    # Redis 缓存失效时间配置（秒）
    REDIS_DEFAULT_EXPIRE: int = 3600  # 默认1小时
    REDIS_SHORT_EXPIRE: int = 300     # 短期缓存5分钟
    REDIS_LONG_EXPIRE: int = 86400    # 长期缓存24小时
    REDIS_USER_SESSION_EXPIRE: int = 7200  # 用户会话2小时
    
    # 支付配置
    PAYMENT_SECRET_KEY: Optional[str] = None
    PAYMENT_WEBHOOK_SECRET: Optional[str] = None
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    # 第三方API配置
    LLM_API_KEY: Optional[str] = None

# 创建设置实例
settings = Settings() 