from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, DateTime, JSON, Enum, Boolean, Float
from app.db.models.base import Base
from app.schemas.enums import CaseType, RelationType

class Case(Base):
    """案例表"""
    __tablename__ = "cases"

    case_id = Column(String, primary_key=True, index=True)
    user_id = Column(String, nullable=False, index=True)  # 案例所属用户ID
    user_name = Column(String, index=True)
    gender = Column(String, nullable=False)
    birth_time_solar = Column(DateTime, nullable=False, index=True)
    birth_time_lunar = Column(String, nullable=False)
    
    # 四柱
    bazi_year = Column(String, nullable=False)
    bazi_month = Column(String, nullable=False)
    bazi_day = Column(String, nullable=False)
    bazi_time = Column(String, nullable=False)
    
    # 出生地点
    birth_place = Column(String, nullable=False)
    
    # 是否使用真太阳时
    useTrueSolarTime = Column(Boolean, nullable=False)
    # 是否是夏令时
    isDST = Column(Boolean, nullable=False)
    # 是否使用早晚子时
    useEarlyOrLateNight = Column(Boolean, nullable=False)
    # 经度
    longitude = Column(Float, nullable=False)
    
    # 关系类型
    relation_type = Column(Enum(RelationType, values_callable=lambda obj: [e.value for e in obj], native_enum=False), nullable=False)
    
    # 标签
    tags = Column(JSON, default=list)
    
    # 测算类型
    case_type = Column(Enum(CaseType, values_callable=lambda obj: [e.value for e in obj], native_enum=False), nullable=False)
    
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    class Config:
        orm_mode = True