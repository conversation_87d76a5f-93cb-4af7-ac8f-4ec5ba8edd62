from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field

class CaseMongoData(BaseModel):
    """案例MongoDB数据模型"""
    case_id: str = Field(..., description="关联PostgreSQL中的case_id")
    divinate_result: Optional[dict] = Field(None, description="测算结果")
    comment: Optional[str] = Field(None, description="备注信息")
    version: int = Field(default=1, description="版本号")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class CaseMongoHistory(BaseModel):
    """案例历史记录模型"""
    case_id: str = Field(..., description="关联PostgreSQL中的case_id")
    divinate_result: Optional[dict] = Field(None, description="历史测算结果")
    comment: Optional[str] = Field(None, description="历史备注信息")
    version: int = Field(..., description="版本号")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_by: Optional[str] = Field(None, description="更新者")
    update_reason: Optional[str] = Field(None, description="更新原因")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }