from sqlalchemy import <PERSON><PERSON><PERSON>, String, Enum, DateTime, Integer
from sqlalchemy.orm import Mapped, mapped_column
import enum
from datetime import datetime
from app.db.models.base import Base

class UserRole(enum.Enum):
    """用户角色枚举"""
    REGULAR = "regular"  # 普通用户
    PREMIUM = "premium"  # 高级会员
    ADMIN = 'admin'      # 管理员

class User(Base):
    """用户模型"""
    
    # 不需要重新定义id字段，直接使用基类的自增整数id
    __tablename__ = "users"
    user_id : Mapped[str] = mapped_column(String(24), primary_key=True, unique=True, index=True, nullable=False)
    user_name : Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    hashed_password : Mapped[str] = mapped_column(String(255), nullable=False)
    is_active : Mapped[bool] = mapped_column(Boolean, default=True)
    
    # 用户信息
    phone : Mapped[str] = mapped_column(String(20), nullable=True, index=True, unique=True)
    email : Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=True)
    
    # 账户状态
    role : Mapped[UserRole] = mapped_column(Enum(UserRole, values_callable=lambda obj: [e.value for e in obj], native_enum=False), default=UserRole.REGULAR)
    
    # 会员相关
    membership_expires_at : Mapped[datetime] = mapped_column(DateTime, nullable=True)
    
    # 其他用户属性
    birth_date : Mapped[datetime] = mapped_column(DateTime, nullable=True)  # 出生日期，用于命理分析
    birth_time : Mapped[str] = mapped_column(String(5), nullable=True)  # 出生时间，HH:MM格式
    birth_place : Mapped[str] = mapped_column(String(255), nullable=True)  # 出生地点
    bazi_year : Mapped[int] = mapped_column(Integer, nullable=True)  # 八字年份
    bazi_month : Mapped[int] = mapped_column(Integer, nullable=True)  # 八字月份
    bazi_day : Mapped[int] = mapped_column(Integer, nullable=True)  # 八字日
    bazi_hour : Mapped[int] = mapped_column(Integer, nullable=True)  # 八字时
    lunar_year : Mapped[int] = mapped_column(Integer, nullable=True)  # 农历年份
    lunar_month : Mapped[int] = mapped_column(Integer, nullable=True)  # 农历月份
    lunar_day : Mapped[int] = mapped_column(Integer, nullable=True)  # 农历日
    lunar_hour : Mapped[int] = mapped_column(Integer, nullable=True)  # 农历时
    gender : Mapped[str] = mapped_column(String(10), nullable=True)  # 性别
    
    # 统计
    login_count : Mapped[int] = mapped_column(Integer, default=1)  # 登录次数
    
    # 关系
    # TODO: 添加与用户相关的关系，如保存的命盘、课程进度等 

    def is_admin(self):
        return self.role == UserRole.ADMIN