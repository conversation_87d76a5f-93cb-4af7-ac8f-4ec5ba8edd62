from typing import List, Optional, Dict, Any, Tuple
import logging
from datetime import datetime
from sqlalchemy import select, update, or_
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.repositories.base_repository import BaseRepository
from app.db.models.case import Case
from app.db.models.case_mongo import CaseMongoData, CaseMongoHistory
from app.schemas.case_schema import CaseCreate, CaseUpdate, CaseSearchParams
from app.schemas.enums import RelationType
from app.core.config import settings
from app.db.mongodb_manager import AsyncMongoDBManager
from app.utils.common import get_uuid

logger = logging.getLogger(__name__)


class CaseRepository(BaseRepository):
    """案例数据访问层"""

    def __init__(self, pg_db: Optional[AsyncSession] = None, mongo_db: Optional[AsyncMongoDBManager] = None, redis_db=None):
        """初始化案例Repository"""
        super().__init__(pg_db, mongo_db, redis_db)
        # mongoDB collection names
        self.case_collection_name = settings.MONGODB_CASE_COLLECTION
        self.case_history_collection_name = settings.MONGODB_CASE_HISTORY_COLLECTION
        if mongo_db:
            self.case_collection = mongo_db.get_collection(
                self.case_collection_name)
            self.case_history_collection = mongo_db.get_collection(
                self.case_history_collection_name)

    async def create_case(self, case_data: CaseCreate, user_id: str) -> Optional[Case]:
        """创建新的案例"""
        self.check_db_dependencies(needs_pg=True, needs_mongo=True)

        # 生成唯一case_id
        case_id = str(get_uuid())

        # 创建PostgreSQL记录
        db_case = Case(
            case_id=case_id,
            user_id=user_id,
            user_name=case_data.user_name,
            gender=case_data.gender,
            birth_time_solar=case_data.birth_time_solar,
            birth_time_lunar=case_data.birth_time_lunar,
            bazi_year=case_data.bazi_year,
            bazi_month=case_data.bazi_month,
            bazi_day=case_data.bazi_day,
            bazi_time=case_data.bazi_time,
            birth_place=case_data.birth_place,
            useTrueSolarTime=case_data.useTrueSolarTime,
            isDST=case_data.isDST,
            useEarlyOrLateNight=case_data.useEarlyOrLateNight,
            longitude=case_data.longitude,
            relation_type=case_data.relation_type,
            tags=case_data.tags,
            case_type=case_data.case_type
        )
        logger.info(f"create_case db_case: {db_case}")
        try:
            # 保存PostgreSQL数据
            self.pg_db.add(db_case)
            await self.pg_db.commit()
            await self.pg_db.refresh(db_case)

            # 只有当divinate_result或comment不为None时才保存MongoDB数据
            if case_data.divinate_result is not None or case_data.comment is not None:
                mongo_data = CaseMongoData(
                    case_id=case_id,
                    divinate_result=case_data.divinate_result,
                    comment=case_data.comment,
                    version=1
                )
                await self.case_collection.insert_one(mongo_data.model_dump())

            return db_case
        except Exception as e:
            logger.error(f"创建案例失败: {str(e)}", exc_info=True)
            await self.pg_db.rollback()
            # 删除MongoDB记录
            if await self.case_collection.find_one({"case_id": case_id}):
                await self.case_collection.delete_one({"case_id": case_id})
            raise

    async def get_case(self, case_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取单个案例的完整信息"""
        self.check_db_dependencies(needs_pg=True, needs_mongo=True)
        if (not user_id):
            return None

        try:
            # 获取PostgreSQL数据
            query = select(Case).filter(
                Case.case_id == case_id, Case.user_id == user_id)

            result = await self.pg_db.execute(query)
            db_case = result.scalars().first()
            if not db_case:
                return None

            # 初始化基础数据
            case_dict = {
                **db_case.__dict__,
                "divinate_result": None,
                "comment": None
            }

            # 获取MongoDB数据（如果存在）
            mongo_data = await self.case_collection.find_one({"case_id": case_id})
            if mongo_data:
                case_dict["divinate_result"] = mongo_data.get(
                    "divinate_result")
                case_dict["comment"] = mongo_data.get("comment")

            return case_dict
        except Exception as e:
            logger.error(f"获取案例失败: {str(e)}", exc_info=True)
            raise

    async def update_case(
        self,
        case_id: str,
        case_update: CaseUpdate,
        user_id: str,
        update_reason: Optional[str] = None,
        updated_by: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """更新案例信息"""
        self.check_db_dependencies(needs_pg=True, needs_mongo=True)

        try:
            # 获取现有案例并校验权限
            result = await self.pg_db.execute(
                select(Case).filter(Case.case_id ==
                       case_id, Case.user_id == user_id)
            )
            db_case = result.scalars().first()
            if not db_case:
                return None

            # 更新PostgreSQL数据
            update_data = case_update.model_dump(exclude_unset=True)
            update_fields = {}

            for field, value in update_data.items():
                if field not in ["divinate_result", "comment"]:
                    update_fields[field] = value

            if update_fields:
                update_stmt = (
                    update(Case)
                    .where(Case.case_id == case_id)
                    .values(**update_fields)
                    .returning(Case)
                )
                result = await self.pg_db.execute(update_stmt)
                await self.pg_db.commit()
                db_case = result.scalars().first()

            # 如果需要更新MongoDB数据
            if "divinate_result" in update_data or "comment" in update_data:
                # 获取当前MongoDB数据
                current_mongo = await self.case_collection.find_one({"case_id": case_id})
                if current_mongo:
                    # 保存历史记录
                    history = CaseMongoHistory(
                        case_id=case_id,
                        divinate_result=current_mongo.get("divinate_result"),
                        comment=current_mongo.get("comment"),
                        version=current_mongo["version"],
                        updated_by=updated_by,
                        update_reason=update_reason
                    )
                    await self.case_history_collection.insert_one(history.model_dump())

                    # 更新MongoDB数据
                    new_version = current_mongo["version"] + 1
                    mongo_update_fields = {
                        "version": new_version,
                        "updated_at": datetime.now()
                    }
                    if "divinate_result" in update_data:
                        mongo_update_fields["divinate_result"] = update_data["divinate_result"]
                    if "comment" in update_data:
                        mongo_update_fields["comment"] = update_data["comment"]

                    await self.case_collection.update_one(
                        {"case_id": case_id},
                        {"$set": mongo_update_fields}
                    )

            return await self.get_case(case_id, user_id)
        except Exception as e:
            logger.error(f"更新案例失败: {str(e)}")
            await self.pg_db.rollback()
            raise

    async def search_cases(
        self,
        params: CaseSearchParams,
        user_id: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """搜索案例"""
        self.check_db_dependencies(needs_pg=True, needs_mongo=True)
        logger.info(f"search_cases params: {params}")
        try:
            if (not user_id):
                return [], 0
            # 构建基础查询
            query = select(Case).filter(Case.user_id == user_id)

            # 应用过滤条件
            if params.user_name:
                query = query.filter(
                    Case.user_name.ilike(f"%{params.user_name}%"))
            if params.birth_time_solar:
                query = query.filter(
                    Case.birth_time_solar == params.birth_time_solar)
            if params.birth_time_lunar:
                query = query.filter(
                    Case.birth_time_lunar == params.birth_time_lunar)
            if params.bazi_year:
                query = query.filter(Case.bazi_year == params.bazi_year)
            if params.bazi_month:
                query = query.filter(Case.bazi_month == params.bazi_month)
            if params.bazi_day:
                query = query.filter(Case.bazi_day == params.bazi_day)
            if params.bazi_time:
                query = query.filter(Case.bazi_time == params.bazi_time)
            if params.tags:
                query = query.filter(Case.tags.overlap(params.tags))
            if params.case_type:
                query = query.filter(Case.case_type == params.case_type)
            if params.relation_type:
                query = query.filter(Case.relation_type ==
                                     params.relation_type)
            else:
                # 默认查询非system case
                query = query.filter(Case.relation_type != RelationType.SYSTEM)

            logger.info(f"search_cases query: {query}")
            # 获取总数
            result = await self.pg_db.execute(query)
            all_cases = result.scalars().all()
            total = len(all_cases)
            logger.info(f"search_cases found {total} cases in PostgreSQL")

            # 应用默认排序和分页
            query = query.order_by(Case.relation_type.desc(), Case.user_name).offset(
                (params.page - 1) * params.page_size).limit(params.page_size)
            result = await self.pg_db.execute(query)
            cases = result.scalars().all()

            # 如果需要全文搜索
            if params.search_text:
                case_ids = [case.case_id for case in cases]
                mongo_filter = {
                    "case_id": {"$in": case_ids},
                    "$or": [
                        {"divinate_result": {
                            "$regex": params.search_text, "$options": "i"}},
                        {"comment": {"$regex": params.search_text, "$options": "i"}}
                    ]
                }
                mongo_cases = await self.case_collection.find(mongo_filter).to_list(None)
                filtered_case_ids = [case["case_id"] for case in mongo_cases]
                cases = [
                    case for case in cases if case.case_id in filtered_case_ids]
                total = len(cases)

            # 获取MongoDB数据并合并
            result = []
            for case in cases:
                mongo_data = await self.case_collection.find_one({"case_id": case.case_id})
                case_dict = {
                    **case.__dict__,
                    "divinate_result": mongo_data.get("divinate_result") if mongo_data else None,
                    "comment": mongo_data.get("comment") if mongo_data else None
                }
                result.append(case_dict)
            return result, total
        except Exception as e:
            logger.error(f"搜索案例失败: {str(e)}", exc_info=True)
            raise  # 重新抛出异常，而不是返回空结果

    async def get_case_history(self, case_id: str, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取案例的历史记录"""
        self.check_db_dependencies(needs_mongo=True)
        
        try:
            if (not user_id):
                return []
            
            case_exists = await self.pg_db.execute(
                select(Case).filter(Case.case_id == case_id, Case.user_id == user_id)
            )
            if not case_exists.scalars().first():
                return []
            
            history = await self.case_history_collection.find(
                {"case_id": case_id}
            ).sort("version", -1).to_list(None)
            return history
        except Exception as e:
            logger.error(f"获取案例历史记录失败: {str(e)}", exc_info=True)
            raise

    async def delete_case(self, case_id: str, user_id: str) -> bool:
        """删除案例（包括PostgreSQL和MongoDB中的数据）"""
        self.check_db_dependencies(needs_pg=True, needs_mongo=True)
        
        try:
            # 检查案例是否存在并校验权限
            result = await self.pg_db.execute(
                select(Case).filter(Case.case_id == case_id, Case.user_id == user_id)
            )
            db_case = result.scalars().first()
            if not db_case:
                return False
            
            # 删除PostgreSQL数据
            await self.pg_db.delete(db_case)
            await self.pg_db.commit()
            
            # 删除MongoDB中的案例数据
            await self.case_collection.delete_one({"case_id": case_id})
            
            # 删除MongoDB中的历史记录
            await self.case_history_collection.delete_many({"case_id": case_id})
            
            logger.info(f"成功删除案例: {case_id}")
            return True
        except Exception as e:
            logger.error(f"删除案例失败: {str(e)}")
            await self.pg_db.rollback()
            raise