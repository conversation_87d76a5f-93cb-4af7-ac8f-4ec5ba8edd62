from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
import logging
from fastapi import Depends, HTTPException, status, Cookie, Request
from fastapi.security import OAuth2PasswordBearer
from app.dependencies.database import get_postgres_session, get_mongodb_manager, get_redis_cache
from app.db.mongodb_manager import AsyncMongoDBManager
from app.services.auth_service import AuthService
from app.services.book_service import BookService
from app.services.case_service import CaseService
from app.core.cache import RedisCache
from app.db.models.user import User
from app.schemas.auth_schemas import CurrentUser

logger = logging.getLogger(__name__)
# 定义OAuth2密码流
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login_json")

# 定义可选的OAuth2密码流（用于非必须登录的接口）
optional_oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login_json", auto_error=False)

# 依赖函数，用于在FastAPI路由中获取AuthService实例
def get_auth_service(db: AsyncSession = Depends(get_postgres_session)) -> AuthService:
    try:
        return AuthService(db)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取AuthService实例失败: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="用户鉴权失败") from e

# 依赖函数，用于在FastAPI路由中获取BookService实例
def get_book_service(db: AsyncSession = Depends(get_postgres_session),
                     mongodb: AsyncMongoDBManager = Depends(get_mongodb_manager),
                     redis: RedisCache = Depends(get_redis_cache)) -> BookService:
    try:
        return BookService(db, mongodb, redis)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取书籍服务实例失败: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务初始化错误") from e

# 依赖函数，用于在FastAPI路由中获取当前用户完整信息
async def get_current_user(auth_service: AuthService = Depends(get_auth_service),
                           #access_token: str = Cookie(None)) -> User:
                           token: str = Depends(oauth2_scheme)) -> User:
    try:
        user = await auth_service.get_current_user(token)
        return user
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取当前用户基本信息失败: {e}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效用户") from e

# 依赖函数，用于在FastAPI路由中获取当前用户基本信息（轻量级）
async def get_current_user_info(auth_service: AuthService = Depends(get_auth_service),
                            #access_token: str = Cookie(None)) -> User:
                            token: str = Depends(oauth2_scheme)) -> CurrentUser:
    try:
        user_info = await auth_service.get_current_user_info(token)
        return user_info
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取当前用户基本信息失败: {e}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效用户") from e

# 依赖函数，用于在FastAPI路由中获取当前用户基本信息（轻量级，可选）
async def get_current_user_info_optional(auth_service: AuthService = Depends(get_auth_service),
                                         #access_token: Optional[str] = Cookie(None)) -> Optional[CurrentUser]:
                                         token: Optional[str] = Depends(optional_oauth2_scheme)) -> Optional[CurrentUser]:
    if token is None:
        return None
    
    try:
        user_info = await auth_service.get_current_user_info(token)
        return user_info
    except HTTPException:
        # 如果token无效，返回None而不是抛出异常
        return None
    except Exception:
        return None

async def get_case_service(
    db: AsyncSession = Depends(get_postgres_session),
    mongo_db: AsyncMongoDBManager = Depends(get_mongodb_manager),
    redis_db: RedisCache = Depends(get_redis_cache)
) -> CaseService:
    """获取案例服务实例"""
    try:
        return CaseService(db, mongo_db, redis_db)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取案例服务实例失败: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)) from e