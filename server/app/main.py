import logging
import sys
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.router import api_router

# 设置日志
logging.basicConfig(
    level=settings.LOG_LEVEL,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)  # 强制输出到 stdout
    ]
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动事件
    from app.db.mongodb_manager import init_async_mongodb
    from app.db.postgres_manager import init_async_postgres
    from app.core.cache import init_redis_cache
    # from py_iztro import Astro

    # 初始化数据库和缓存连接，存储在app.state中
    app.state.postgres_manager = init_async_postgres(settings)
    app.state.mongodb_manager = init_async_mongodb(settings)
    app.state.redis_cache = init_redis_cache(settings)
    # 初始化紫微斗数计算器
    # app.state.astro = Astro()
    
    try:
        await app.state.postgres_manager.verify_connection()
        await app.state.mongodb_manager.verify_connection()
        await app.state.redis_cache.is_connected()
    except Exception as e:
        logger.error("数据库连接失败:%s", e)
        raise RuntimeError("启动失败，数据库无法连接") from e
    #logger.info("数据库和缓存连接池初始化完成")
    
    yield
    
    # 关闭事件
    # 关闭数据库连接池
    await app.state.postgres_manager.close()
    
    # 关闭Redis连接池
    await app.state.redis_cache.close()
    
    # 关闭MongoDB连接
    await app.state.mongodb_manager.close()
    
    logger.info("数据库连接资源已释放")

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    description="命理学探索研究室API",
    version="0.1.0",
    openapi_url="/api/openapi.json" if settings.APP_ENV != "production" else None,
    docs_url="/api/docs" if settings.APP_ENV != "production" else None,
    redoc_url="/api/redoc" if settings.APP_ENV != "production" else None,
    lifespan=lifespan,
)

# 添加全局异常处理中间件
@app.middleware("http")
async def global_exception_handler(request: Request, call_next):
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        logger.error(f"全局异常处理: {request.method} {request.url} - {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"message": f"服务器内部错误: {str(e)}"}
        )

# # 添加日志中间件
# @app.middleware("http")
# async def log_requests(request: Request, call_next):
#     logger.info(f"收到请求: {request.method} {request.url}")
#     headers = dict(request.headers)
#     logger.info(f"请求头: {headers}")

#     # 读取请求体
#     body = await request.body()
#     if body:
#         try:
#             # 尝试以JSON格式解码并打印
#             logger.info(f"请求体 (JSON): {body.decode('utf-8')}")
#         except UnicodeDecodeError:
#             # 如果解码失败，打印原始字节
#             logger.info(f"请求体 (raw): {body}")
#     else:
#         logger.info("请求体为空")

#     response = await call_next(request)
#     return response


# 配置CORS, 允许前端域名跨域访问，携带 Cookie
origins = settings.CORS_ORIGINS.split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(api_router, prefix="/api")

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "ok"}

@app.exception_handler(HTTPException)
async def db_connection_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.detail}
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8080, reload=True, workers=1) 