from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict, field_validator
from app.schemas.enums import CaseType, RelationType

# 基础档案
class CaseBase(BaseModel):
    case_id: str
    user_id: str
    user_name: str
    # 性别
    gender: str 
    # 出生时间: 公历 例如：2025-06-05 12:00:00
    birth_time_solar: datetime
    # 出生时间: 农历 例如：2025年六月初五子时
    birth_time_lunar: str 

    #  四柱 例如："戊辰 己未 甲戌 己巳"
    bazi_year: str
    bazi_month: str
    bazi_day: str
    bazi_time: str

    # 出生地点: 例如："北京,北京,东城区"
    birth_place: str 

    # 是否使用真太阳时
    useTrueSolarTime: bool
    # 是否是夏令时
    isDST: bool
    # 是否使用早晚子时
    useEarlyOrLateNight: bool
    # 经度
    longitude: float

    # 测算结果, 可为空
    divinate_result: Optional[dict] = None

    # 用户自己的备注
    comment: Optional[str] = None

    # 关系类型
    relation_type: RelationType

    # tags, 用户填写，可为空
    tags: List[str] = []

    # 测算类型
    case_type: CaseType

    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class CaseCreate(BaseModel):
    user_name: str = Field(..., min_length=1, max_length=50, description="用户姓名")
    gender: str = Field(..., description="性别")
    birth_time_solar: datetime = Field(..., description="公历出生时间")
    birth_time_lunar: str = Field(..., min_length=1, description="农历出生时间")
    bazi_year: str = Field(..., min_length=1, description="八字年柱")
    bazi_month: str = Field(..., min_length=1, description="八字月柱")
    bazi_day: str = Field(..., min_length=1, description="八字日柱")
    bazi_time: str = Field(..., min_length=1, description="八字时柱")
    birth_place: str = Field(..., min_length=1, description="出生地点")
    useTrueSolarTime: bool = Field(..., description="是否使用真太阳时")
    isDST: bool = Field(..., description="是否是夏令时")
    useEarlyOrLateNight: bool = Field(..., description="是否使用早晚子时")
    longitude: float = Field(..., description="经度")
    divinate_result: Optional[dict] = Field(None, description="测算结果")
    comment: Optional[str] = Field(None, description="用户备注")
    relation_type: RelationType = Field(..., description="关系类型")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    case_type: CaseType = Field(..., description="案例类型")
    
    @field_validator('gender')
    def validate_gender(cls, v):
        if v not in ['male', 'female', '男', '女']:
            raise ValueError('性别必须是 male, female, 男 或 女')
        return v

    @field_validator('birth_time_solar')
    def validate_birth_time_solar(cls, v):
        if isinstance(v, str):
            try:
                from datetime import datetime
                # 尝试解析本地时间格式: "YYYY-MM-DD HH:MM:SS"
                if len(v) == 19 and v[4] == '-' and v[7] == '-' and v[10] == ' ' and v[13] == ':' and v[16] == ':':
                    return datetime.strptime(v, '%Y-%m-%d %H:%M:%S')
                # 尝试解析缺少秒的格式: "YYYY-MM-DD HH:MM"
                elif len(v) == 16 and v[4] == '-' and v[7] == '-' and v[10] == ' ' and v[13] == ':':
                    return datetime.strptime(v + ':00', '%Y-%m-%d %H:%M:%S')
                # 尝试解析ISO格式作为备选
                else:
                    return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                raise ValueError('出生时间格式不正确，请使用 YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD HH:MM 格式')
        return v

class CaseUpdate(BaseModel):
    user_name: Optional[str] = None
    gender: Optional[str] = None
    birth_time_solar: Optional[datetime] = None
    birth_time_lunar: Optional[str] = None
    bazi_year: Optional[str] = None
    bazi_month: Optional[str] = None
    bazi_day: Optional[str] = None
    bazi_time: Optional[str] = None
    birth_place: Optional[str] = None
    divinate_result: Optional[dict] = None
    comment: Optional[str] = None
    relation_type: Optional[RelationType] = None
    tags: Optional[List[str]] = None

class CaseSearchParams(BaseModel):
    user_name: Optional[str] = None
    birth_time_solar: Optional[datetime] = None
    birth_time_lunar: Optional[str] = None
    bazi_year: Optional[str] = None
    bazi_month: Optional[str] = None
    bazi_day: Optional[str] = None
    bazi_time: Optional[str] = None
    tags: Optional[List[str]] = None
    case_type: Optional[CaseType] = None
    relation_type: Optional[RelationType] = None
    search_text: Optional[str] = None  # 用于全文检索divinate_result和comment
    page: int = 1
    page_size: int = 20

class CaseResponse(CaseBase):
    pass

class CaseListResponse(BaseModel):
    total: int
    items: List[CaseResponse]
    page: int
    page_size: int