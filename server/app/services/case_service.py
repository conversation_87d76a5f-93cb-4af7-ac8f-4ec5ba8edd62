from typing import Optional, List, Dict, Any, Tuple
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.mongodb_manager import AsyncMongoDBManager
from app.db.repositories.case_repository import CaseRepository
from app.schemas.case_schema import (
    CaseCreate,
    CaseUpdate,
    CaseSearchParams,
)
from app.schemas.auth_schemas import CurrentUser

logger = logging.getLogger(__name__)

# 目前缓存功能尚未实现，所以redis_db参数暂时未使用
class CaseService:
    """案例服务"""

    def __init__(
        self,
        pg_db: AsyncSession,
        mongo_db: AsyncMongoDBManager,
        redis_db = None
    ):
        self.case_repository = CaseRepository(pg_db, mongo_db, redis_db)

    async def create_case(
        self,
        case_data: CaseCreate,
        current_user: Optional[CurrentUser] = None
    ) -> Optional[str]:
        """
        创建新的案例
        
        Args:
            case_data: 案例创建数据
            current_user: 当前用户信息
        """
        try:
            if not current_user or not current_user.user_id:
                raise ValueError("用户未登录")
            
            case = await self.case_repository.create_case(case_data, current_user.user_id)
            if case:
                return case.case_id
            return None
        except Exception as e:
            logger.error(f"创建案例失败: {str(e)}")
            raise

    async def get_case(
        self,
        case_id: str,
        current_user: Optional[CurrentUser] = None
    ) -> Optional[Dict[str, Any]]:
        """
        获取案例详情
        
        Args:
            case_id: 案例ID
            current_user: 当前用户信息
        """
        try:
            user_id = current_user.user_id if current_user else None
            return await self.case_repository.get_case(case_id, user_id)
        except Exception as e:
            logger.error(f"获取案例失败: {str(e)}")
            raise

    async def update_case(
        self,
        case_id: str,
        case_update: CaseUpdate,
        update_reason: Optional[str] = None,
        current_user: Optional[CurrentUser] = None
    ) -> Optional[Dict[str, Any]]:
        """
        更新案例信息
        
        Args:
            case_id: 案例ID
            case_update: 更新数据
            update_reason: 更新原因
            current_user: 当前用户信息
        """
        try:
            if not current_user or not current_user.user_id:
                raise ValueError("用户未登录")
            
            updated_by = current_user.user_id
            return await self.case_repository.update_case(
                case_id,
                case_update,
                current_user.user_id,
                update_reason=update_reason,
                updated_by=updated_by
            )
        except Exception as e:
            logger.error(f"更新案例失败: {str(e)}")
            raise

    async def search_cases(
        self,
        params: CaseSearchParams,
        current_user: Optional[CurrentUser] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        搜索案例
        
        Args:
            params: 搜索参数
            current_user: 当前用户信息
        """
        try:
            user_id = current_user.user_id if current_user else None
            return await self.case_repository.search_cases(params, user_id)
        except Exception as e:
            logger.error(f"搜索案例失败: {str(e)}")
            raise

    async def get_case_history(
        self,
        case_id: str,
        current_user: Optional[CurrentUser] = None
    ) -> List[Dict[str, Any]]:
        """
        获取案例历史记录
        
        Args:
            case_id: 案例ID
            current_user: 当前用户信息
        """
        try:
            user_id = current_user.user_id if current_user else None
            return await self.case_repository.get_case_history(case_id, user_id)
        except Exception as e:
            logger.error(f"获取案例历史记录失败: {str(e)}")
            raise

    async def delete_case(
        self,
        case_id: str,
        current_user: Optional[CurrentUser] = None
    ) -> bool:
        """
        删除案例
        
        Args:
            case_id: 案例ID
            current_user: 当前用户信息
        """
        try:
            if not current_user or not current_user.user_id:
                raise ValueError("用户未登录")
            
            return await self.case_repository.delete_case(case_id, current_user.user_id)
        except Exception as e:
            logger.error(f"删除案例失败: {str(e)}")
            raise