from uuid import uuid4
import base64
import string
import time
from typing import Optional

def get_short_uuid() -> str:
    """生成短UUID（11字符，无特殊字符）
    使用base64编码但替换特殊字符，确保URL安全且无-_字符
    """
    u = uuid4()
    encoded = base64.urlsafe_b64encode(u.bytes[:8]).rstrip(b'=').decode('utf-8')
    # 替换 - 和 _ 为字母数字字符
    return encoded.replace('-', '0').replace('_', '1')

def get_uuid() -> str:
    """生成标准UUID（22字符，无特殊字符）
    使用base64编码但替换特殊字符，确保URL安全且无-_字符
    """
    u = uuid4()
    encoded = base64.urlsafe_b64encode(u.bytes).rstrip(b'=').decode('utf-8')
    # 替换 - 和 _ 为字母数字字符
    return encoded.replace('-', '0').replace('_', '1')

def get_pure_hex_uuid() -> str:
    """生成纯十六进制UUID（32字符，仅包含0-9a-f）
    优点：纯数字字母，无特殊字符，性能最佳
    """
    return uuid4().hex

def get_short_hex_uuid() -> str:
    """生成短十六进制UUID（16字符，仅包含0-9a-f）
    基于uuid4的前8字节，适合短ID需求
    """
    return uuid4().hex[:16]

def get_time_index(time_zhi: str) -> int:
    """根据时辰获取时间索引"""
    time_index = {
        "子": 0,
        "丑": 1,
        "寅": 2,
        "卯": 3,
        "辰": 4,
        "巳": 5,
        "午": 6,
        "未": 7,
        "申": 8,
        "酉": 9,
        "戌": 10,
        "亥": 11,
    }
    return time_index[time_zhi]